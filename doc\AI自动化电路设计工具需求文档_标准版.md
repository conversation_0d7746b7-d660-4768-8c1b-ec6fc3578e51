# 一份基于LangGraph与ngspice的AI驱动电路设计自动化工具蓝图

## 第一部分：系统架构与基础组件

本部分详细阐述了自动化电路设计工具的顶层架构设计、核心技术选型及其理论基础。其目标是为后续的详细设计与实现提供一个清晰、稳固的框架。

### 1.1. 多智能体主管（Supervisor）架构：一种聚焦智能的范式

为了应对自动化电路设计这一复杂且需要迭代优化的任务，本方案采用了一种分层的、多智能体的主管（Supervisor）架构。该架构将一个宏大的任务分解为多个由专业化智能体（Agent）处理的子任务，并通过一个中心化的主管智能体进行协调与调度。

实践证明，相较于单一的、庞大的“全能型”智能体，这种架构通过强制实现任务的专业化，可以显著提升系统的可靠性、可控性和最终性能。

单一智能体在面对过多工具或过于复杂的任务时，往往会出现“认知过载”，导致其在决策时表现不佳，难以选择最合适的工具或执行路径。通过将职责划分给不同的专业智能体，每个智能体只需关注其有限的领域和工具集，从而能够更精确、更高效地完成任务。

主管-工作者（Supervisor-Worker）模式正是实现这种专业化分工的理想选择，它将复杂的控制流逻辑集中在主管身上，而将具体的执行工作下放给各个工作者智能体。

#### 智能体角色定义

本系统由一个主管智能体和四个专业化的工作者智能体构成。每个智能体在LangGraph中都表现为一个或多个节点，它们共同协作，完成从用户需求理解到最终电路设计的完整闭环。

- **Supervisor（主管）**: 系统的中心调度器和路由器。它本身不执行具体的业务任务，而是作为整个工作流的“大脑”，根据当前的设计状态（State）决定下一步应该调用哪个工作者智能体。其核心逻辑在LangGraph中通过条件边（Conditional Edges）实现，负责管理智能体之间的任务交接（handoffs）。

- **PlannerAgent（规划智能体）**: 系统的“前门”和需求分析师。它负责接收并理解用户的自然语言请求（例如，“我需要一个1kHz的低通滤波器”），并将其转化为结构化的、机器可读的设计目标（DesignGoal）。

- **CircuitDesignerAgent（电路设计智能体）**: 系统的“电路工程师”。它接收来自PlannerAgent的结构化目标以及来自CritiqueAgent的反馈，负责生成或修改电路的SPICE网表（netlist）。此智能体专注于电路拓扑和元件参数的调整。

- **SimulationAgent（仿真智能体）**：系统的“实验室技术员”。这是一个确定性的、非LLM驱动的节点，其本质是一个工具（Tool）。它接收CircuitDesignerAgent生成的电路网表，并调用ngspice仿真引擎执行电路性能仿真。

- **CritiqueAgent（评估智能体）**: 系统的“分析与品控师”。它负责评估SimulationAgent返回的仿真结果，将其与DesignGoal进行量化比较，并生成定性的、具有指导意义的反馈意见，供CircuitDesignerAgent在下一轮迭代中参考。

#### 工作流可视化

整个系统的核心是一个迭代式的设计-仿真-评估循环。主管智能体确保这个循环能够有序进行，直到设计目标达成或达到预设的终止条件。其高层工作流可以通过以下Mermaid图清晰地展示：

```mermaid
graph TD
    %% 定义节点
    A[用户请求]
    B[PlannerAgent]
    C{Supervisor 决策}
    D[CircuitDesignerAgent]
    E[SimulationAgent]
    F[CritiqueAgent]
    G[结果]
    
    %% 定义连接关系
    A --> B
    B --> C
    C -- 设计目标已定义 --> D
    D -- 网表已生成/修改 --> C
    C -- 网表待仿真 --> E
    E -- 仿真完成 --> C
    C -- 结果待评估 --> F
    F -- 评估完成, 未达标 --> D
    F -- 评估完成, 已达标 --> G
    C -- 其他条件 --> G
```

**表 4.1: 智能体职责与接口**

| 智能体名称 | 图节点名称 | 核心职责 | 关键状态输入 | 关键状态输出 |
| :--- | :--- | :--- | :--- | :--- |
| Supervisor(路由函数) | 任务调度与流程控制 | AppState (整个状态) | 下一个节点的名称 (字符串) | |
| PlannerAgent | planner_node | 将自然语言请求转换为结构化设计目标 | user_request | design_goal |
| CircuitDesignerAgent | circuit_designer_node | 生成或修改电路网表 | design_goal, critique_history, circuit_model | circuit_model (新版本) |
| SimulationAgent | simulation_node | 执行ngspice电路仿真 | circuit_model, simulation_params | simulation_results |
| CritiqueAgent | critique_node | 评估仿真结果，生成反馈 | design_goal, simulation_results | critique_history (追加新反馈) |

### 1.2. 核心技术深度解析：LangGraph与PySpice作为引擎与桥梁

技术选型的正确性是项目成功的基石。本方案选择LangGraph作为智能流程编排框架，选择PySpice作为与ngspice仿真引擎交互的桥梁，这一组合旨在最大化系统的灵活性、鲁棒性和开发效率。

#### LangGraph：为有状态、循环工作流而生

传统的许多工作流框架基于有向无环图（DAG），这对于需要迭代和修正的复杂任务（如电路设计）存在天然的局限性。LangGraph的设计理念深受Pregel和Apache Beam的启发，其核心优势在于原生支持循环（Cycles）。这使得构建一个“设计-仿真-评估-修正”的闭环流程变得直观且高效。

- **StateGraph**: LangGraph的核心是`StateGraph`类，它代表了一个有状态的图。与无状态的函数链不同，`StateGraph`维护一个中心化的状态对象（State Object），该对象在图的各个节点之间传递和更新。每个节点执行其任务后，返回对状态的更新操作，而不是简单地将输出传递给下一个节点。这种机制使得整个应用的状态变迁清晰可追溯。

- **状态更新机制**: 状态对象中的属性可以通过两种方式更新：完全覆盖或累加。特别是通过`typing.Annotated`与`operator.add`的结合，可以非常方便地实现对列表类型状态（如消息历史、评估反馈历史）的累加操作。这对于记录系统的迭代历史至关重要，因为它为智能体提供了避免重复错误和进行更深层次推理所需的所有上下文。

选择LangGraph，本质上是选择了一种能够精确建模复杂、循环、有状态交互的计算范式，这与自动化电路设计的内在需求完全契合。

#### PySpice：连接Python世界与SPICE引擎的稳固桥梁

ngspice是一个功能强大且经过验证的开源电路仿真器，但它是一个用C语言编写的、主要通过命令行交互的传统工具。直接在现代Python应用中通过`subprocess`调用ngspice，会面临诸多挑战：脆弱的命令行字符串拼接、复杂的进程管理、以及对非结构化输出文件的繁琐解析。

PySpice库的出现完美地解决了这些问题，它为ngspice提供了一个高级的、Pythonic的封装层。选择PySpice而非直接调用，是本项目中一项关键的风险规避决策。

- **面向对象的网表构建**: PySpice允许通过面向对象的方式构建电路网表，例如使用`circuit.R(1, 'n1', 'n2', 1@u_kOhm)`来定义一个电阻。这种方式对于大型语言模型（LLM）而言，比生成和操作复杂的SPICE纯文本字符串要结构化得多，也更不容易出错。CircuitDesignerAgent的任务从“生成一段符合SPICE语法的文本”转变为“调用一系列Python函数”，这极大地降低了任务的复杂度和出错率。

- **无缝的数据管道**: PySpice能够将ngspice的仿真结果直接作为NumPy数组返回给Python环境。这构建了一个从仿真到分析的无缝数据管道。CritiqueAgent可以直接消费结构化的NumPy数组进行数值计算，而无需编写脆弱的解析器来处理ngspice输出的`.raw`文件。

通过PySpice，我们将底层复杂且异构的ngspice核心完全封装在一个稳固的抽象层之后。这使得整个系统的上层逻辑可以完全在Python原生环境中进行，所有的数据交换都通过定义良好的Python对象（Pydantic模型）和数据结构（NumPy数组）完成。这种架构选择将一个“脆弱的文本与进程管理问题”转变为一个“稳固的Python对象操作问题”，从而极大地提升了整个系统的鲁棒性和可维护性。

## 第二部分：状态管理与数据模式

在基于`StateGraph`的架构中，一个明确、完整且强类型的中心状态模式（Schema）是整个系统的基石。它不仅是所有智能体之间的数据契约，也是一种有效的“提示工程”（Prompt Engineering）手段，能够约束LLM的行为，确保输出的结构化和一致性。

本部分将使用Pydantic模型来定义系统中的所有核心数据结构。

### 2.1. 中心状态模式：应用的唯一事实来源

`AppState`是整个应用生命周期中所有状态信息的容器。它被定义为一个`TypedDict`，并作为`StateGraph`的泛型参数。其中，`critique_history`字段使用`Annotated[list, operator.add]`来确保每次评估后，新的反馈会被追加到历史记录中，而不是覆盖。

**表 2.1: 中心状态模式定义 (AppState)**

| 键名 | Pydantic / Python 类型 | 描述 | 主要写入者 |
| :--- | :--- | :--- | :--- |
| `user_request` | `str` | 用户输入的原始自然语言请求。 | (初始状态) |
| `design_goal` | `Optional[DesignGoal]` | 由PlannerAgent解析出的结构化设计目标。 | PlannerAgent |
| `circuit_model` | `Optional[CircuitModel]` | 当前正在设计的电路模型。 | CircuitDesignerAgent |
| `simulation_params` | `Optional[SimulationParameters]` | 为下一次仿真准备的参数。 | CritiqueAgent / PlannerAgent |
| `simulation_results` | `Optional[SimulationResult]` | SimulationAgent返回的最新仿真结果。 | SimulationAgent |
| `critique_history` | `Annotated[list[Critique], operator.add]` | 历次评估的反馈记录列表。 | CritiqueAgent |
| `iteration_count` | `int` | 当前的设计迭代次数。 | Supervisor (逻辑中更新) |

定义这些强类型的Pydantic模式，其意义远不止于数据验证。当我们将这些模式作为目标输出格式提供给LLM时，实际上是在引导其进行“结构化思考”。例如，对PlannerAgent的指令不再是模糊的“理解用户意图”，而是精确的“分析用户意图，并以JSON格式填充DesignGoal模型”。这种方式利用了LLM强大的模式匹配和生成能力，同时通过预设的结构约束了其输出空间，显著减少了产生无关或格式错误输出的可能性，从而提升了智能体的可靠性。

此外，`critique_history`字段不仅仅是一个日志。它是整个系统迭代过程中的“短期记忆”。CircuitDesignerAgent可以被明确指示去查阅这份历史，以避免重复之前的失败尝试。主管Supervisor也可以利用这份历史的长度来判断设计过程是否陷入僵局，从而触发超时或寻求人工介入等元级别的控制策略，这使得系统更加健壮。

### 2.2. 电路设计与目标的数据模型

为了将抽象的设计概念转化为精确的计算机指令，需要定义以下数据模型。

#### DesignGoal 模式

该模型用于承载PlannerAgent对用户需求的结构化理解。

```python
from typing import Literal, Optional
from pydantic import BaseModel, Field

class DesignGoal(BaseModel):
    """
    结构化的电路设计目标。
    """
    circuit_type: Literal['low_pass_rc', 'high_pass_rc', 'band_pass_rc'] = Field(
       ..., description="目标电路的类型, 例如 'low_pass_rc'."
    )
    target_metric: Literal['cutoff_frequency', 'gain', 'phase_margin'] = Field(
       ..., description="核心优化指标, 例如 'cutoff_frequency'."
    )
    target_value: float = Field(..., description="目标指标的数值。")
    target_unit: str = Field(..., description="目标指标的单位, 例如 'Hz', 'dB'。")
    tolerance: Optional[float] = Field(0.05, description="目标值的可接受公差范围, 例如 0.05 表示 ±5%。")
```

#### CircuitComponent 与 CircuitModel 模式

这两个模型用于以结构化方式表示电路本身，其设计直接映射到PySpice库的API调用，便于后续的网表生成。

```python
from typing import List, Tuple

class CircuitComponent(BaseModel):
    """
    表示电路中的单个元件。
    """
    name: str = Field(..., description="元件的唯一标识符, 例如 'R1', 'C1'。")
    type: Literal['R', 'C', 'V', 'L'] = Field(..., description="元件类型 (Resistor, Capacitor, Voltage Source, Inductor)。")
    nodes: Tuple[str, str] = Field(..., description="元件连接的两个节点, 例如 ('n1', 'gnd')。")
    value: float = Field(..., description="元件的数值 (单位在生成网表时指定)。")
    unit: str = Field(..., description="元件值的单位, 例如 'kOhm', 'nF', 'V'。")

class CircuitModel(BaseModel):
    """
    表示完整的电路模型。
    """
    title: str = Field("AI Generated Circuit", description="电路的标题。")
    components: List[CircuitComponent] = Field(..., description="构成电路的元件列表。")
```

### 2.3. 仿真输入/输出的数据模型

这些模型定义了`SimulationAgent`工具的输入和输出接口，确保了仿真任务的参数传递和结果回收是明确和可靠的。

#### SimulationParameters 模式

该模型封装了执行一次ngspice仿真所需的所有控制参数，其字段设计参考了ngspice支持的分析类型及其参数。

```python
class SimulationParameters(BaseModel):
    """
    定义仿真任务的参数。
    """
    analysis_type: Literal['ac', 'tran', 'op'] = Field(..., description="要执行的仿真分析类型。")
    
    # AC 分析参数
    start_frequency: Optional[float] = Field(None, description="AC分析的起始频率 (Hz)。")
    stop_frequency: Optional[float] = Field(None, description="AC分析的终止频率 (Hz)。")
    number_of_points: Optional[int] = Field(None, description="AC分析中每个十倍频的点数。")
    
    #... 其他分析类型的参数 (如瞬态分析的 tstep, tstop) 可以按需添加
```

#### SimulationResult 模式

该模型用于封装仿真的输出，无论是成功的数据还是失败的错误信息。这种设计是实现鲁棒错误处理的关键。

```python
from typing import Any  # 用于表示 NumPy 数组

class SimulationResult(BaseModel):
    """
    封装单次仿真运行的结果。
    """
    analysis_type: str = Field(..., description="执行的仿真分析类型。")
    data: Optional[Any] = Field(None, description="仿真成功时返回的数据, 通常是 NumPy 数组。")
    vectors: Optional[List[str]] = Field(None, description="数据中包含的向量名称列表, 例如 ['frequency', 'v(out)']。")
    error: Optional[str] = Field(None, description="仿真失败时返回的错误信息。")

    class Config:
        arbitrary_types_allowed = True
```

## 第三部分：仿真引擎：一个稳固的SimulationAgent工具

`SimulationAgent`是连接智能体世界和物理仿真世界的关键枢纽。它不是一个LLM智能体，而是一个确定性的工具节点，其核心是`NgspiceSimulator`类。

这个类的设计目标不仅仅是调用PySpice，更重要的是构建一个**绝缘和翻译层**：
-   它将上层Pythonic的数据结构（`CircuitModel`）翻译成PySpice可理解的格式。
-   它将底层ngspice可能发生的崩溃或错误（Crash）绝缘，防止其摧毁整个Python主进程。
-   它将错误信息翻译成结构化的`SimulationResult`对象返回给状态机。

### 3.1. 设计NgspiceSimulator工具类

`NgspiceSimulator`类将封装所有与PySpice和ngspice的直接交互。它将在应用启动时被实例化一次，并作为工具注册到LangGraph的`ToolExecutor`中。

```python
import numpy as np
from PySpice.Spice.Netlist import Circuit
from PySpice.Unit import *

# 引用之前定义的Pydantic模型
# from.schemas import CircuitModel, SimulationParameters, SimulationResult

class NgspiceSimulator:
    """
    一个封装了PySpice功能的工具类，用于执行ngspice仿真。
    """
    def __init__(self):
        # 可以在此进行一些初始化设置，例如日志记录
        pass

    def run_ac_analysis(
        self, 
        circuit_model: CircuitModel, 
        params: SimulationParameters
    ) -> SimulationResult:
        #... 实现将在 3.2 和 3.3 节中详述
        pass

    def run_transient_analysis(
        self, 
        circuit_model: CircuitModel, 
        params: SimulationParameters
    ) -> SimulationResult:
        #... 实现细节
        pass

    #... 其他仿真方法
```

### 3.2. 实现核心仿真功能

`NgspiceSimulator`类中的每个方法都将遵循一个标准模式：接收结构化的输入（`CircuitModel`, `SimulationParameters`），执行仿真，然后返回结构化的输出（`SimulationResult`）。

#### `run_ac_analysis` 方法规范

此方法负责执行交流（AC）扫描分析，通常用于获取电路的频率响应，如波特图。

**实现逻辑：**

1.  **输入验证**: 检查`params`是否包含AC分析所需的所有参数（`start_frequency`, `stop_frequency`, `number_of_points`）。
2.  **构建PySpice电路**:
    -   创建一个`PySpice.Spice.Netlist.Circuit`对象，使用`circuit_model.title`。
    -   遍历`circuit_model.components`列表。
    -   对于每个`component`，根据其`type`调用`circuit`对象的相应方法（如`circuit.R`, `circuit.C`, `circuit.V`）。
    -   使用`@u_...`语法将`component.value`和`component.unit`转换为PySpice的单位制，例如`1000@u_Ohm`或`100@u_nF`。
3.  **执行仿真**:
    -   通过`circuit.simulator(temperature=25, nominal_temperature=25)`获取`simulator`实例。
    -   调用`simulator.ac(...)`来执行仿真。
4.  **封装结果**:
    -   从返回的`analysis`对象中提取数据。例如，`frequency = np.array(analysis.frequency)`，`vout = np.array(analysis['node_name'])`。
    -   将所有提取的NumPy数组打包到一个字典或结构中。
    -   创建一个`SimulationResult`实例，填充`analysis_type`, `data`，以及`vectors`。
5.  **返回结果**: 返回创建的`SimulationResult`对象。

### 3.3. 任务关键：防御性编程与错误处理

这是本部分乃至整个项目架构中最为关键的一环。ngspice作为一个外部C库，在接收到不合法或不完整的网表时，有可能会导致段错误（Segmentation Fault）或其他致命错误，这会直接终止调用它的Python父进程。如果不对其进行妥善处理，整个LangGraph应用将极其不稳定。

#### 解决方案

必须将每一次对PySpice仿真方法的调用都置于一个全面的`try...except`块中。这个`except`块必须捕获`Exception`基类，以应对所有可能的预期和非预期异常。

#### 错误传播机制

当`except`块捕获到异常时，绝不能简单地将异常重新抛出。正确的做法是：

1.  捕获异常对象`e`。
2.  创建一个`SimulationResult`实例。
3.  将其`data`和`vectors`字段设置为`None`。
4.  将其`error`字段设置为异常的字符串表示，例如`str(e)`。
5.  返回这个包含错误信息的`SimulationResult`对象。

通过这种方式，仿真失败被转化为一种**可控的状态**，而不是一个不可控的系统崩溃。Supervisor在接收到这个结果后，可以检查`state['simulation_results'].error`字段是否为`None`。如果不为`None`，它就可以将控制流安全地路由回`CircuitDesignerAgent`，并附带明确的错误信息。

**`run_ac_analysis`的完整实现示例（含错误处理）：**

```python
def run_ac_analysis(
    self, 
    circuit_model: CircuitModel, 
    params: SimulationParameters
) -> SimulationResult:
    try:
        # 1. 构建 PySpice 电路
        circuit = Circuit(circuit_model.title)
        # 假设电路中需要一个输入源用于AC分析
        circuit.V('input', 'in', circuit.gnd, 'dc 0 ac 1') 
        
        for comp in circuit_model.components:
            # 这是一个简化的映射，实际需要更完整的实现
            if comp.type == 'R':
                circuit.R(comp.name, *comp.nodes, f"{comp.value}{comp.unit}")
            elif comp.type == 'C':
                circuit.C(comp.name, *comp.nodes, f"{comp.value}{comp.unit}")
        
        # 2. 执行仿真
        simulator = circuit.simulator(temperature=25, nominal_temperature=25)
        analysis = simulator.ac(
            start_frequency=params.start_frequency * u_Hz,
            stop_frequency=params.stop_frequency * u_Hz,
            number_of_points=params.number_of_points,
            variation='dec'
        )

        # 3. 封装成功结果
        output_node = 'out' # 假设输出节点名为 'out'
        result_data = {
            'frequency': np.array(analysis.frequency),
            'vout': np.array(analysis[output_node])
        }
        
        return SimulationResult(
            analysis_type='ac',
            data=result_data,
            vectors=['frequency', 'vout'],
            error=None
        )

    except Exception as e:
        # 4. 封装失败结果
        return SimulationResult(
            analysis_type='ac',
            data=None,
            vectors=None,
            error=f"Ngspice simulation failed: {str(e)}"
        )
```

这种将底层执行失败转化为上层状态变化的模式，是构建与遗留系统或不稳定外部API交互的健壮AI应用的核心设计原则。

## 第四部分：智能体核心：实现LangGraph工作流

本部分将详细说明如何将前面定义的智能体角色、状态和工具组装成一个可执行的LangGraph图。

### 4.1. 初始化StateGraph

工作流的构建始于`StateGraph`的实例化。这个实例将绑定我们在第二部分定义的`AppState`作为其中心状态模式。

```python
from langgraph.graph import StateGraph, END
from typing import TypedDict, Annotated, List, Optional
import operator

# 引用之前定义的 Pydantic 模型
# from.schemas import DesignGoal, CircuitModel, SimulationParameters, SimulationResult, Critique

class AppState(TypedDict):
    """
    中心状态的 TypedDict 定义。
    """
    user_request: str
    design_goal: Optional[DesignGoal]
    circuit_model: Optional[CircuitModel]
    simulation_params: Optional[SimulationParameters]
    simulation_results: Optional[SimulationResult]
    critique_history: Annotated[List[Critique], operator.add]
    iteration_count: int

# 初始化 StateGraph
workflow_builder = StateGraph(AppState)
```
此处的`workflow_builder`就是我们构建整个工作流的蓝图对象。

### 4.2. 定义智能体节点

在LangGraph中，图的节点可以是任何可调用对象（函数或LCEL Runnable），它接收当前状态作为输入，并返回一个包含状态更新的字典。我们将为每个智能体角色定义一个对应的节点函数。

```python
# 节点函数的存根（Stub）定义
def planner_node(state: AppState) -> dict:
    """PlannerAgent的逻辑实现节点。"""
    print("--- Executing Planner Node ---")
    #... LLM调用，将 state['user_request'] 转换为 design_goal
    # 返回: {'design_goal': new_goal, 'simulation_params': params}
    pass

def circuit_designer_node(state: AppState) -> dict:
    """CircuitDesignerAgent的逻辑实现节点。"""
    print("--- Executing Circuit Designer Node ---")
    #... LLM调用，根据 design_goal 和 critique_history 生成/修改 circuit_model
    # 返回: {'circuit_model': updated_model, 'iteration_count': state['iteration_count'] + 1}
    pass

def simulation_node(state: AppState) -> dict:
    """SimulationAgent的逻辑实现节点。"""
    print("--- Executing Simulation Node ---")
    #... 调用 NgspiceSimulator 工具
    # 返回: {'simulation_results': results}
    pass

def critique_node(state: AppState) -> dict:
    """CritiqueAgent的逻辑实现节点。"""
    print("--- Executing Critique Node ---")
    #... 执行确定性计算和LLM综合，生成评估
    # 返回: {'critique_history': [new_critique]}
    pass

# 将节点添加到图中
workflow_builder.add_node("planner", planner_node)
workflow_builder.add_node("designer", circuit_designer_node)
workflow_builder.add_node("simulator", simulation_node)
workflow_builder.add_node("critique", critique_node)
```

### 4.3. 实现主管的条件路由逻辑

这是整个系统的控制核心。我们将通过`add_conditional_edges`方法来实现Supervisor的决策逻辑。一个清晰的实现方式是，将所有主要的路由决策集中到一个单一的路由函数中。这个函数在每个关键节点执行完毕后被调用，决定工作流的下一个走向。

我们将定义一个`supervisor_router`函数，它接收当前的状态，并返回下一个应该被调用的节点的名称（字符串）。

```python
from langgraph.graph import END

MAX_ITERATIONS = 10

def supervisor_router(state: AppState) -> str:
    """
    中心化的主管路由函数，决定下一个节点。
    """
    print("--- SUPERVISOR: Making routing decision ---")
    
    # 检查迭代次数是否超限
    if state.get("iteration_count", 0) >= MAX_ITERATIONS:
        print(f"Decision: Max iterations ({MAX_ITERATIONS}) reached. Ending.")
        return END

    # 初始状态，需要规划
    if state.get("design_goal") is None:
        print("Decision: No design goal. Routing to planner.")
        return "planner"

    # 有目标但无电路，或仿真失败需要重新设计
    last_sim_results = state.get("simulation_results")
    if state.get("circuit_model") is None or (last_sim_results and last_sim_results.error):
        if last_sim_results and last_sim_results.error:
            print(f"Decision: Simulation failed ({last_sim_results.error}). Routing to designer for correction.")
        else:
            print("Decision: Goal defined, no circuit yet. Routing to designer.")
        return "designer"

    # 有电路，需要仿真
    if state.get("simulation_results") is None: # or state_is_dirty(...)
         print("Decision: Circuit exists/updated. Routing to simulator.")
         return "simulator"
    
    # 有仿真结果，需要评估
    if state.get("critique_history") is None: # or state_is_dirty(...)
        print("Decision: Simulation complete. Routing to critique.")
        return "critique"

    # 评估完成，检查是否达标
    last_critique = state.get("critique_history")[-1]
    if last_critique:
        if last_critique.goal_met:
            print("Decision: Goal has been met. Ending.")
            return END
        else:
            print("Decision: Goal not met. Routing back to designer for iteration.")
            return "designer"
            
    # 默认或意外情况
    print("Decision: Default case. Routing to designer.")
    return "designer"
```

一个更符合LangGraph API范式的实现方式是将主管的逻辑分解并附加到每个工作节点的出口处。

```python
# 设置入口点
workflow_builder.set_entry_point("planner")

# 定义条件边
workflow_builder.add_conditional_edges(
    "planner",
    lambda state: "designer"
)

def after_designer_router(state: AppState) -> str:
    return "simulator"

workflow_builder.add_conditional_edges(
    "designer",
    after_designer_router,
    {"simulator": "simulator"}
)

def after_simulator_router(state: AppState) -> str:
    if state["simulation_results"].error:
        return "designer"
    else:
        return "critique"

workflow_builder.add_conditional_edges(
    "simulator",
    after_simulator_router,
    {"designer": "designer", "critique": "critique"}
)

def after_critique_router(state: AppState) -> str:
    if state["iteration_count"] >= MAX_ITERATIONS or state["critique_history"][-1].goal_met:
        return END
    else:
        return "designer"

workflow_builder.add_conditional_edges(
    "critique",
    after_critique_router,
    {"designer": "designer", END: END}
)
```

**表 4.2: 主管条件路由逻辑**

| 源节点 | 条件 | 目标节点 |
| :--- | :--- | :--- |
| START | (无) | `planner` |
| `planner` | (无) | `designer` |
| `designer` | (无) | `simulator` |
| `simulator` | `state['simulation_results'].error is not None` | `designer` |
| `simulator` | `state['simulation_results'].error is None` | `critique` |
| `critique` | `state['iteration_count'] >= MAX_ITERATIONS` | `END` |
| `critique` | `state['critique_history'][-1].goal_met is True` | `END` |
| `critique` | `state['critique_history'][-1].goal_met is False` | `designer` |

## 第五部分：智能体特定提示与逻辑

本部分为每个基于LLM的智能体（Planner, Designer, Critique）提供详细的系统提示（System Prompt）和核心逻辑规范。

### 5.1. PlannerAgent逻辑：从请求到可执行目标

PlannerAgent的作用是将模糊的用户需求精确化。

#### 提示工程 (Prompt Engineering)

> **System Prompt:**
> You are an expert electronics engineer and project manager. Your task is to analyze a user's request for a circuit design and convert it into a structured, machine-readable design goal.
>
> **Instructions:**
> 1.  Carefully read the user's request.
> 2.  Identify the fundamental type of circuit required (e.g., low-pass RC filter).
> 3.  Identify the primary performance metric the user cares about (e.g., cutoff frequency).
> 4.  Extract the target value and its unit for that metric.
> 5.  If the user does not provide enough information, you MUST ask clarifying questions. Do not invent or assume values.
> 6.  Your final output MUST be a single JSON object that strictly conforms to the `DesignGoal` Pydantic schema provided below. Do not add any extra text.
>
> **Pydantic Schema for `DesignGoal`:**
> ```json
> {
>     "title": "DesignGoal",
>     "type": "object",
>     "properties": {
>         "circuit_type": { "type": "string", "enum": ["low_pass_rc", "high_pass_rc", "band_pass_rc"] },
>         "target_metric": { "type": "string", "enum": ["cutoff_frequency", "gain", "phase_margin"] },
>         "target_value": { "type": "number" },
>         "target_unit": { "type": "string" },
>         "tolerance": { "type": "number", "default": 0.05 }
>     },
>     "required": ["circuit_type", "target_metric", "target_value", "target_unit"]
> }
> ```

#### 交互示例

**用户输入**: `"我想设计一个滤波器，把我的音频信号里大约1kHz以上的高频噪声滤掉。"`

**PlannerAgent输出 (JSON)**:
```json
{
  "circuit_type": "low_pass_rc",
  "target_metric": "cutoff_frequency",
  "target_value": 1000,
  "target_unit": "Hz",
  "tolerance": 0.05
}
```

### 5.2. CircuitDesignerAgent逻辑：迭代式设计引擎

CircuitDesignerAgent是核心的创造性智能体。它必须能够根据目标和反馈，迭代地优化电路参数。

#### 提示工程 (Prompt Engineering)

> **System Prompt:**
> You are a highly skilled circuit design specialist. Your task is to generate or modify a circuit to meet a specific design goal, taking into account feedback from previous simulation attempts.
>
> **Current Context:**
> - **Design Goal:** `{design_goal_json}`
> - **Current Circuit Model:** `{circuit_model_json}`
> - **Most Recent Critique:** `{last_critique_text}`
> - **Critique History:** `{critique_history_json}`
>
> **Instructions:**
> 1.  Analyze the **Design Goal**.
> 2.  Review the **Most Recent Critique**.
> 3.  Consult the **Critique History** to avoid repeating past mistakes.
> 4.  Propose a modification to the **Current Circuit Model** by only modifying the `value` of the components.
> 5.  Your output MUST be a single JSON object that strictly conforms to the `CircuitModel` Pydantic schema.
>
> **Pydantic Schema for `CircuitModel`:**
> ```json
> {
>     "title": "CircuitModel",
>     "type": "object",
>     "properties": {
>         "title": { "type": "string" },
>         "components": {
>             "type": "array",
>             "items": {
>                 "type": "object",
>                 "properties": {
>                     "name": { "type": "string" },
>                     "type": { "type": "string" },
>                     "nodes": { "type": "array", "items": { "type": "string" } },
>                     "value": { "type": "number" },
>                     "unit": { "type": "string" }
>                 },
>                 "required": ["name", "type", "nodes", "value", "unit"]
>             }
>         }
>     },
>     "required": ["title", "components"]
> }
> ```

#### 工具定义

为了让LLM能够安全地修改电路，它不会直接生成JSON。取而代之，它将被赋予一系列工具（Functions/Tools），用于修改状态中的`CircuitModel`对象。例如，可以定义一个`update_component_value(name: str, new_value: float, new_unit: str)`工具。

### 5.3. CritiqueAgent逻辑：量化分析与定性反馈的结合

CritiqueAgent结合了计算机精确的计算能力和LLM卓越的语言综合能力。

#### 1. 确定性分析函数

这些是纯Python函数，直接操作`SimulationResult`中的NumPy数组。

-   **`calculate_cutoff_frequency(freq_array, gain_array_db)`**: 找到通带增益，计算-3dB点增益，然后使用插值（`np.interp`）找到对应的精确频率值。
-   **`calculate_passband_gain(gain_array_db)`**: 对于低通滤波器，取频率数组前1%部分的增益平均值作为通带增益。

#### 2. LLM驱动的综合与判断

在确定性函数计算出所有关键性能指标（KPIs）后，这些指标将连同设计目标一起被送入CritiqueAgent的LLM中。

> **System Prompt:**
> You are an expert quality control engineer. Your job is to compare the simulated performance of a circuit against its design goal and provide a clear, concise critique.
>
> **Analysis Data:**
> - **Design Goal:** `{design_goal_json}`
> - **Simulated Performance Metrics:** `{simulated_metrics_json}`
>
> **Instructions:**
> 1.  Compare each simulated metric with the design goal.
> 2.  Determine if the design meets the goal within the specified tolerance.
> 3.  Synthesize your findings into a concise, helpful feedback string for the designer.
> 4.  Your final output MUST be a single JSON object that strictly conforms to the `Critique` Pydantic schema.
>
> **Pydantic Schema for `Critique`:**
> ```json
> {
>     "title": "Critique",
>     "type": "object",
>     "properties": {
>         "goal_met": { "type": "boolean" },
>         "feedback": { "type": "string" },
>         "metrics": { "type": "object" }
>     },
>     "required": ["goal_met", "feedback", "metrics"]
> }
> ```

## 第六部分：最终组装、执行与实例演示

本部分将所有组件整合在一起，展示如何编译最终的LangGraph应用。

### 6.1. 编译图并定义工具集

在定义了所有节点和边之后，最后一步是调用`compile()`方法将`StateGraph`构建成一个可执行的应用。为了实现记忆，可以传入一个`checkpointer`。

```python
from langgraph.checkpoint.memory import MemorySaver

#... (前面已定义 workflow_builder 和所有节点与边)

# 编译图
memory = MemorySaver()
app = workflow_builder.compile(checkpointer=memory)
```

### 6.2. 主应用循环

下面的`main.py`脚本展示了如何驱动这个应用。它负责处理用户输入、管理会话、调用图的`stream()`方法，并实时打印出每个节点的输出。

```python
import uuid

#... (导入 app 和 AppState)

def main():
    """
    主应用函数，处理用户交互和图的执行。
    """
    thread_id = str(uuid.uuid4())
    config = {"configurable": {"thread_id": thread_id}}

    while True:
        user_input = input("User: ")
        if user_input.lower() in ["quit", "exit"]:
            print("Exiting...")
            break

        initial_state = AppState(
            user_request=user_input,
            critique_history=[],
            iteration_count=0
        )

        events = app.stream(initial_state, config, stream_mode="values")
        for event in events:
            for key, value in event.items():
                print(f"--- Output from node: {key} ---")
                print(value)
                print("\n")

if __name__ == "__main__":
    main()
```

### 6.3. 实例演示：设计一个1kHz低通RC滤波器

本节将详细追踪一次完整的交互，以验证整个系统的功能符合设计要求。

**用户输入**: `User: I need a low-pass filter with a cutoff frequency of 1kHz.`

#### 执行追踪

1.  **初始状态**: `app.stream()`被调用，`user_request`被设置。
2.  **`planner_node` 执行**:
    -   **输入**: `state['user_request']`
    -   **输出**: `state['design_goal']` 被填充为 `{ "circuit_type": "low_pass_rc", ... }`。
3.  **`designer_node` 执行 (迭代 1)**:
    -   **输入**: `state['design_goal']`
    -   **逻辑**: LLM根据 $f_c = \frac{1}{2 \pi RC}$ 进行启发式初始猜测。
    -   **输出**: `state['circuit_model']` 被填充，`iteration_count`变为1。
4.  **`simulation_node` 执行 (迭代 1)**:
    -   **输入**: `state['circuit_model']`
    -   **输出**: `state['simulation_results']` 被填充，`data`字段包含NumPy数组。
5.  **`critique_node` 执行 (迭代 1)**:
    -   **输入**: `state['design_goal']`, `state['simulation_results']`
    -   **逻辑**: `calculate_cutoff_frequency`计算出实际截止频率约为1591 Hz。LLM生成反馈。
    -   **输出**: `state['critique_history']` 被追加新记录：`{ "goal_met": false, "feedback": "Cutoff frequency is too high...", ... }`。
6.  **`designer_node` 执行 (迭代 2)**:
    -   **输入**: `state['design_goal']`, `state['critique_history']`
    -   **逻辑**: LLM看到反馈，决定增加电容C1的值。
    -   **输出**: `state['circuit_model']`被更新，`iteration_count`变为2。
7.  **循环继续...** 直到截止频率在容差范围内。
8.  **`critique_node` 执行 (最终迭代)**:
    -   **逻辑**: 计算出的截止频率为995 Hz，在容差范围内。
    -   **输出**: `state['critique_history']` 追加记录：`{ "goal_met": true, ... }`。
9.  **路由到 END**: `supervisor_router`检测到`goal_met`为`true`，返回`END`，工作流结束。

#### 最终结果可视化

在工作流结束后，可以从最终状态的`simulation_results`中提取数据，并使用`matplotlib`生成波特图。

```python
import matplotlib.pyplot as plt
import numpy as np

final_state = app.get_state(config)
results = final_state['simulation_results']

if results and results.data:
    freq = results.data['frequency']
    vout_complex = results.data['vout']

    mag_db = 20 * np.log10(np.abs(vout_complex))
    phase_deg = np.angle(vout_complex, deg=True)

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)

    ax1.semilogx(freq, mag_db)
    ax1.set_title('Bode Plot')
    ax1.set_ylabel('Magnitude (dB)')
    ax1.grid(which='both', linestyle='--')

    ax2.semilogx(freq, phase_deg)
    ax2.set_ylabel('Phase (degrees)')
    ax2.set_xlabel('Frequency (Hz)')
    ax2.grid(which='both', linestyle='--')

    plt.show()
```

## 结论与建议

本报告为构建一个结合了大型语言模型与传统科学计算的自动化电路设计工具，提供了一份详尽的、可执行的架构蓝图。

### 核心结论

-   **架构选择是成功的关键**: 采用基于LangGraph的主管-工作者多智能体架构，是解决此类复杂、迭代式设计任务的理想范式。
-   **抽象层是鲁棒性的保障**: 使用PySpice作为ngspice的Pythonic封装层是本项目最重要的技术决策之一，它将底层C库的复杂性和潜在的不稳定性完全隔离。
-   **结构化提示是可靠性的基石**: 通过要求LLM智能体输出符合预定义JSON Schema的数据，我们极大地约束了其输出空间，保证了数据流转的一致性和可靠性。
-   **混合智能是准确性的前提**: CritiqueAgent所采用的“确定性计算 + LLM综合”混合模式，是确保评估结果准确可信的有效方法。

### 实施建议

-   **分步实现与测试**: 建议按照本报告的结构，分步实现和单元测试每个组件。
-   **迭代式扩展**: 本蓝图以RC滤波器为例，未来可以在此框架上进行扩展，支持更复杂的电路类型。
-   **引入人类在环（Human-in-the-Loop）**: LangGraph原生支持在工作流中加入暂停和人工干预的节点。在系统遇到设计僵局时，可将状态呈现给人类工程师进行决策。
</final_file_content>

IMPORTANT: For any future changes to this file, use the final_file_content shown above as your reference. This content reflects the current state of the file, including any auto-formatting (e.g., if you used single quotes but the formatter converted them to double quotes). Always base your SEARCH/REPLACE operations on this final version to ensure accuracy.<environment_details>
# VSCode Visible Files
../../request_b2e9ef1b-6eb8-43b4-8382-01bd645aa45b/0
../../response_daf4e1bb-d754-4991-94d7-f77533adc72a/0
../../response_18621bcc-2765-44c2-a0bf-5ef078ac5be8/0
../../response_97e6805b-8962-406c-a3c3-a61a6e67c9ee/0
AI自动化电路设计工具需求文档_标准版.md

# VSCode Open Tabs
AI自动化电路设计工具需求文档_标准版.md

# Current Time
7/19/2025, 6:06:02 PM (Asia/Shanghai, UTC+8:00)

# Current Mode
ACT MODE
</environment_details>
