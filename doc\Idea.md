1. 用langgraph 和 ngspice两个工具来设计一个自动化设计电路的自动化工具
- 该怎么写好需求文档，给AI编码工具作为输入，一步一步的最后生成代码，完成工具的开发
- 需求要细化，比如函数level，让擅长编码的LLM按照函数说明逐个生成函数

2. 使用脚本对仿真结果进行分析，避免LLM直接读取日志文件进行分析
- 日志可能很大，LLM上下文不够
- 节约token
- 最重要的是使用脚本分析才准确

3. 使用标准markdown格式输出

4. mermaid先定义Node再定义连接

5. 设置最大迭代次数后，还没完成设计目标就结束

6. 当达到一定仿真迭代次数后，要总结前面成功和失败的经验作为全新的开始，避免上下文过长导致LLM截断输入，也可以节约token
- 特别要保留每次仿真后分析数据得出的针对设计的结论

7. 使用 PySpice ，方便电路的定义和数据的处理

8. 电路不要局限于某种电路

9. 电路是否符合设计要求的判断应该交给硬编码程序，确保精确性，大模型只需要获取判断结果

10. 