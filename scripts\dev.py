#!/usr/bin/env python3
"""
开发辅助脚本

使用uv管理项目的常用开发任务
"""

import subprocess
import sys
import argparse
from pathlib import Path

def run_command(cmd, description=""):
    """运行命令并处理错误"""
    if description:
        print(f"🔄 {description}")
    
    print(f"💻 Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=False)
    
    if result.returncode != 0:
        print(f"❌ Command failed with exit code {result.returncode}")
        sys.exit(result.returncode)
    else:
        print(f"✅ {description or 'Command'} completed successfully")
    
    return result

def install_deps(full=False, dev=False):
    """安装依赖"""
    cmd = ["uv", "sync"]
    
    if full:
        cmd.append("--extra=full")
    if dev:
        cmd.append("--extra=dev")
    
    run_command(cmd, "Installing dependencies")

def run_tests(coverage=False, file=None):
    """运行测试"""
    cmd = ["uv", "run", "pytest"]
    
    if coverage:
        cmd.extend(["--cov=src/circuit_agent", "--cov-report=html", "--cov-report=term"])
    
    if file:
        cmd.append(file)
    else:
        cmd.append("tests/")
    
    run_command(cmd, "Running tests")

def format_code():
    """格式化代码"""
    run_command(["uv", "run", "black", "src/", "tests/"], "Formatting code with black")
    run_command(["uv", "run", "isort", "src/", "tests/"], "Sorting imports with isort")

def lint_code():
    """代码检查"""
    run_command(["uv", "run", "flake8", "src/", "tests/"], "Linting code with flake8")
    run_command(["uv", "run", "mypy", "src/"], "Type checking with mypy")

def run_app():
    """运行应用"""
    run_command(["uv", "run", "python", "main.py"], "Starting application")

def build_project():
    """构建项目"""
    run_command(["uv", "build"], "Building project")

def clean():
    """清理构建文件"""
    import shutil
    
    dirs_to_clean = [
        "dist",
        "build", 
        "*.egg-info",
        "__pycache__",
        ".pytest_cache",
        ".coverage",
        "htmlcov"
    ]
    
    for pattern in dirs_to_clean:
        for path in Path(".").glob(f"**/{pattern}"):
            if path.is_dir():
                print(f"🗑️  Removing directory: {path}")
                shutil.rmtree(path)
            elif path.is_file():
                print(f"🗑️  Removing file: {path}")
                path.unlink()

def main():
    parser = argparse.ArgumentParser(description="Development helper script")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Install command
    install_parser = subparsers.add_parser("install", help="Install dependencies")
    install_parser.add_argument("--full", action="store_true", help="Install full dependencies")
    install_parser.add_argument("--dev", action="store_true", help="Install dev dependencies")
    
    # Test command
    test_parser = subparsers.add_parser("test", help="Run tests")
    test_parser.add_argument("--coverage", action="store_true", help="Run with coverage")
    test_parser.add_argument("--file", help="Run specific test file")
    
    # Format command
    subparsers.add_parser("format", help="Format code")
    
    # Lint command
    subparsers.add_parser("lint", help="Lint code")
    
    # Run command
    subparsers.add_parser("run", help="Run application")
    
    # Build command
    subparsers.add_parser("build", help="Build project")
    
    # Clean command
    subparsers.add_parser("clean", help="Clean build files")
    
    # Check command (format + lint + test)
    subparsers.add_parser("check", help="Run all checks (format + lint + test)")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == "install":
            install_deps(full=args.full, dev=args.dev)
        elif args.command == "test":
            run_tests(coverage=args.coverage, file=args.file)
        elif args.command == "format":
            format_code()
        elif args.command == "lint":
            lint_code()
        elif args.command == "run":
            run_app()
        elif args.command == "build":
            build_project()
        elif args.command == "clean":
            clean()
        elif args.command == "check":
            format_code()
            lint_code()
            run_tests()
            print("🎉 All checks passed!")
    
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
