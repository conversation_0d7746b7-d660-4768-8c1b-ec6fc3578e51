[project]
name = "langspice-v2"
version = "0.1.0"
description = "AI-driven circuit design automation tool using LangGraph and ngspice"
readme = "README.md"
requires-python = ">=3.8"
authors = [
    { name = "AI Assistant", email = "<EMAIL>" }
]
keywords = ["circuit", "design", "automation", "ai", "ngspice", "langgraph"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Electronic Design Automation (EDA)",
]

dependencies = [
    "pydantic>=2.0.0",
    "numpy>=1.20.0",
    "typing-extensions>=4.0.0",
]

[project.optional-dependencies]
full = [
    "langchain[anthropic]>=0.3.26",
    "langgraph>=0.5.3",
    "langsmith>=0.4.8",
    "PySpice>=1.5.0",
]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[project.scripts]
langspice = "src.circuit_agent.main:main"

[project.urls]
Homepage = "https://github.com/example/langspice-v2"
Repository = "https://github.com/example/langspice-v2.git"
Documentation = "https://github.com/example/langspice-v2#readme"
"Bug Tracker" = "https://github.com/example/langspice-v2/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/circuit_agent"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--tb=short",
    "--strict-markers",
]

[tool.black]
line-length = 88
target-version = ["py38"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["circuit_agent"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "PySpice.*",
    "langgraph.*",
    "langchain.*",
]
ignore_missing_imports = true
