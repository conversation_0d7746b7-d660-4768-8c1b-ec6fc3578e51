# -----------------------------------------------------------------------------
# PySpice 结构化学习示例：RC 电路瞬态分析 (V2)
#
# 这个脚本将：
# 1. 使用函数和 main 入口，结构更清晰。
# 2. 自动创建 ./output 目录来存放所有结果。
# 3. 为所有输出文件添加时间戳，方便管理。
# 4. 将运行日志、仿真图表和原始数据分别保存为 .txt, .png, .csv 文件。
# -----------------------------------------------------------------------------

import os
import logging
from datetime import datetime

# 导入必要的科学计算和绘图库
import numpy as np
import matplotlib.pyplot as plt
from math import exp

# 导入 PySpice 库
from PySpice.Spice.Netlist import Circuit
from PySpice.Unit import *

# --- 全局配置 ---
OUTPUT_DIR = 'output'


def setup_logging(log_filepath):
    """配置日志系统，将日志同时输出到控制台和文件。"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filepath),
            logging.StreamHandler()
        ]
    )


def create_rc_circuit():
    """
    创建并返回一个 RC 低通滤波器电路对象。
    :return: PySpice Circuit 对象
    """
    circuit = Circuit('RC 低通滤波器')
    circuit.V('input', 'input', circuit.gnd, 5@u_V)
    circuit.R(1, 'input', 'output', 1@u_kOhm)
    circuit.C(1, 'output', circuit.gnd, 1@u_uF)
    return circuit


def run_simulation(circuit):
    """
    对给定的电路进行瞬态分析。
    :param circuit: PySpice Circuit 对象
    :return: analysis 对象，包含仿真结果
    """
    simulator = circuit.simulator(temperature=25, nominal_temperature=25)
    analysis = simulator.transient(step_time=10@u_us, end_time=5@u_ms)
    return analysis


def save_plot(analysis, filepath):
    """
    将仿真结果绘制成图表并保存到文件。
    :param analysis: 包含仿真结果的 analysis 对象
    :param filepath: 图片保存路径
    """
    time_s = analysis.time
    v_in = analysis.nodes['input']
    v_out = analysis.nodes['output']

    plt.figure(figsize=(10, 6))
    plt.title('RC 电路瞬态分析', fontproperties="SimHei")
    plt.xlabel('时间 (s)')
    plt.ylabel('电压 (V)')
    plt.grid(True)
    
    plt.plot(time_s, v_in, label='V(input) - 输入电压')
    plt.plot(time_s, v_out, label='V(output) - 电容电压')

    # 标记时间常数 tau = 1ms
    tau = 1e-3
    v_at_tau = 5 * (1 - exp(-1))
    plt.axvline(x=tau, color='red', linestyle='--', label=f'时间常数 τ = 1ms')
    plt.plot(tau, v_at_tau, 'ro')
    plt.text(tau, v_at_tau - 0.5, f'{v_at_tau:.2f}V (63.2%)', color='red')
    
    plt.legend()
    plt.savefig(filepath)  # 保存图表到文件
    plt.close()            # 关闭图表，释放内存


def save_data_to_csv(analysis, filepath):
    """
    将仿真数据导出为 CSV 文件。
    :param analysis: 包含仿真结果的 analysis 对象
    :param filepath: CSV 文件保存路径
    """
    time_s = np.array(analysis.time)
    v_in = np.array(analysis.nodes['input'])
    v_out = np.array(analysis.nodes['output'])

    # 将数据合并到一个数组中，并转置，使每一行代表一个时间点
    data_to_save = np.vstack((time_s, v_in, v_out)).T
    
    # 使用 numpy.savetxt 保存，并添加表头
    np.savetxt(
        filepath,
        data_to_save,
        delimiter=',',
        header='Time(s),V_in(V),V_out(V)',
        comments='' # 必须设置为空字符串，否则表头会带'#'
    )


def main():
    """主执行函数"""
    # --- 1. 设置输出路径和文件名 ---
    # 如果 output 目录不存在，则创建它
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 创建带时间戳的文件名前缀
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    base_filename = f"rc_simulation_{timestamp}"
    
    # 定义所有输出文件的完整路径
    log_filepath = os.path.join(OUTPUT_DIR, f"{base_filename}_log.txt")
    plot_filepath = os.path.join(OUTPUT_DIR, f"{base_filename}_plot.png")
    data_filepath = os.path.join(OUTPUT_DIR, f"{base_filename}_data.csv")

    # --- 2. 配置日志 ---
    setup_logging(log_filepath)
    
    # --- 3. 创建和仿真电路 ---
    logging.info("脚本开始执行。")
    logging.info("正在创建 RC 电路...")
    rc_circuit = create_rc_circuit()
    
    # 将 SPICE 网表记录到日志
    logging.info(f"成功创建电路，SPICE 网表如下:\n\n{str(rc_circuit)}\n")
    
    logging.info("开始进行瞬态分析...")
    analysis_result = run_simulation(rc_circuit)
    logging.info("仿真完成。")
    
    # --- 4. 保存结果 ---
    logging.info(f"正在将图表保存到: {plot_filepath}")
    save_plot(analysis_result, plot_filepath)
    
    logging.info(f"正在将数据保存到: {data_filepath}")
    save_data_to_csv(analysis_result, data_filepath)
    
    logging.info("所有任务完成，脚本执行结束。")


# --- 脚本入口 ---
if __name__ == "__main__":
    main()