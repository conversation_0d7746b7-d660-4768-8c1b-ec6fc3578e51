# LangSpice v2 - AI自动化电路设计工具

🔌 基于LangGraph和ngspice的智能电路设计系统

## 项目简介

LangSpice v2是一个使用大型语言模型(LLM)和传统电路仿真工具相结合的自动化电路设计系统。该系统采用多智能体架构，能够根据用户的自然语言描述自动设计和优化电路。

### 核心特性

- 🤖 **多智能体架构**: 基于LangGraph的主管-工作者模式
- 🔧 **智能电路设计**: 自动生成和优化电路参数
- 📊 **精确仿真分析**: 集成ngspice进行电路仿真
- 🎯 **目标驱动优化**: 根据设计目标自动迭代优化
- 🌐 **多语言支持**: 支持中英文自然语言输入
- 🔄 **迭代式设计**: 基于仿真反馈持续改进设计

### 支持的电路类型

- 低通RC滤波器 (Low-pass RC Filter)
- 高通RC滤波器 (High-pass RC Filter)
- 带通RC滤波器 (Band-pass RC Filter)

## 系统架构

```mermaid
graph TD
    A[用户请求] --> B[PlannerAgent]
    B --> C[CircuitDesignerAgent]
    C --> D[SimulationAgent]
    D --> E[CritiqueAgent]
    E --> F{目标达成?}
    F -->|是| G[输出结果]
    F -->|否| C
    D --> H{仿真失败?}
    H -->|是| C
    H -->|否| E
```

### 智能体说明

1. **PlannerAgent**: 解析用户需求，生成结构化设计目标
2. **CircuitDesignerAgent**: 根据目标和反馈设计/修改电路
3. **SimulationAgent**: 调用ngspice执行电路仿真
4. **CritiqueAgent**: 分析仿真结果，提供改进建议

## 安装和使用

### 环境要求

- Python 3.8+
- uv (Python包管理器)
- 可选: PySpice (用于真实仿真)
- 可选: LangGraph (用于完整功能)

### 安装步骤

1. 安装uv (如果尚未安装)
```bash
# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. 克隆项目
```bash
git clone <repository-url>
cd langspice_v2
```

3. 安装依赖
```bash
uv sync
```

4. 运行程序
```bash
uv run python main.py
```

### uv常用命令

```bash
# 添加新依赖
uv add <package-name>

# 更新依赖
uv sync --upgrade

# 在虚拟环境中运行命令
uv run <command>
```

### 使用示例

启动程序后，您可以输入如下请求：

```
🤖 请输入您的电路设计需求: 我需要一个1kHz的低通滤波器

🤖 请输入您的电路设计需求: I need a high-pass filter with cutoff frequency of 500Hz

🤖 请输入您的电路设计需求: 设计一个截止频率为2kHz的低通RC滤波器
```

## 项目结构

```
langspice_v2/
├── src/circuit_agent/          # 主要源代码
│   ├── __init__.py
│   ├── schemas/                # 数据模型定义
│   │   ├── __init__.py
│   │   └── models.py
│   ├── tools/                  # 工具类
│   │   ├── __init__.py
│   │   └── ngspice_simulator.py
│   ├── agents/                 # 智能体实现
│   │   ├── __init__.py
│   │   ├── planner.py
│   │   ├── designer.py
│   │   ├── simulator.py
│   │   └── critique.py
│   └── app.py                  # LangGraph应用
├── tests/                      # 测试代码
│   ├── __init__.py
│   ├── test_agents.py
│   └── test_tools.py
├── doc/                        # 文档
├── main.py                     # 主入口
└── README.md
```

## 运行测试

```bash
# 运行所有测试
uv run python -m pytest tests/

# 运行特定测试
uv run python -m pytest tests/test_agents.py
uv run python -m pytest tests/test_tools.py

# 或使用unittest
uv run python -m unittest tests.test_agents
uv run python -m unittest tests.test_tools
```



## 技术特点

### 1. 防御性编程
- 完善的错误处理机制
- 仿真失败时的优雅降级
- 输入验证和边界检查

### 2. 模块化设计
- 清晰的职责分离
- 可扩展的架构
- 标准化的接口

### 3. 混合智能
- LLM负责创造性任务
- 确定性算法负责精确计算
- 结构化数据交换

## 配置选项

### 仿真参数
- 最大迭代次数: 10次
- 默认容差: ±5%
- 频率扫描点数: 100点

### 日志配置
程序会自动生成日志文件 `circuit_agent.log`，记录详细的执行过程。

## 扩展开发

### 添加新的电路类型
1. 在 `schemas/models.py` 中扩展 `circuit_type` 枚举
2. 在 `agents/designer.py` 中添加对应的设计函数
3. 更新相关的测试用例

### 添加新的分析类型
1. 在 `tools/ngspice_simulator.py` 中实现新的分析方法
2. 在 `agents/critique.py` 中添加对应的评估函数
3. 更新数据模型和测试

## 故障排除

### 常见问题

1. **PySpice未安装**: 程序会自动使用模拟数据运行
2. **LangGraph未安装**: 程序会使用简化的工作流
3. **仿真失败**: 检查电路设计的合理性

### 调试模式
启用详细日志：
```bash
# 使用uv运行调试模式
uv run python main.py --debug

# 或设置环境变量
export PYTHONPATH=./src
uv run python main.py
```

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 致谢

- [LangGraph](https://github.com/langchain-ai/langgraph) - 工作流编排框架
- [PySpice](https://github.com/FabriceSalvaire/PySpice) - Python SPICE接口
- [ngspice](http://ngspice.sourceforge.net/) - 开源电路仿真器