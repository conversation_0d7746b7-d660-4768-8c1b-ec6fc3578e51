"""
PlannerAgent实现

实现PlannerAgent的核心逻辑，将用户的自然语言请求转换为结构化的DesignGoal对象。
"""

import json
import logging
from typing import Dict, Any

from ..schemas.models import AppState, DesignGoal, SimulationParameters


def planner_node(state: AppState) -> Dict[str, Any]:
    """
    PlannerAgent在LangGraph中的实现节点。
    此节点调用LLM，将用户的自然语言请求转换为结构化的DesignGoal对象。

    Args:
        state: 当前的LangGraph应用状态，主要使用 state['user_request']。

    Returns:
        一个字典，用于更新状态中的 'design_goal' 和 'simulation_params'。
    """
    logger = logging.getLogger(__name__)
    logger.info("--- Executing Planner Node ---")
    
    user_request = state.get('user_request', '')
    
    if not user_request:
        logger.error("No user request found in state")
        return {}
    
    try:
        # 这里应该调用LLM来解析用户请求
        # 为了演示，我们使用简单的关键词匹配
        design_goal = _parse_user_request(user_request)
        
        # 根据设计目标设置仿真参数
        simulation_params = _create_simulation_params(design_goal)
        
        logger.info(f"Generated design goal: {design_goal}")
        logger.info(f"Generated simulation params: {simulation_params}")
        
        return {
            'design_goal': design_goal,
            'simulation_params': simulation_params
        }
        
    except Exception as e:
        logger.error(f"Error in planner_node: {e}")
        return {}


def _parse_user_request(user_request: str) -> DesignGoal:
    """
    解析用户请求并生成DesignGoal
    
    在实际实现中，这里应该调用LLM来进行智能解析。
    目前使用简单的关键词匹配作为演示。
    """
    user_request_lower = user_request.lower()
    
    # 确定电路类型
    if 'low pass' in user_request_lower or 'lowpass' in user_request_lower or '低通' in user_request_lower:
        circuit_type = 'low_pass_rc'
    elif 'high pass' in user_request_lower or 'highpass' in user_request_lower or '高通' in user_request_lower:
        circuit_type = 'high_pass_rc'
    elif 'band pass' in user_request_lower or 'bandpass' in user_request_lower or '带通' in user_request_lower:
        circuit_type = 'band_pass_rc'
    else:
        # 默认为低通滤波器
        circuit_type = 'low_pass_rc'
    
    # 提取频率信息
    target_value, target_unit = _extract_frequency(user_request)
    
    # 确定目标指标
    if 'gain' in user_request_lower or '增益' in user_request_lower:
        target_metric = 'gain'
    elif 'phase' in user_request_lower or '相位' in user_request_lower:
        target_metric = 'phase_margin'
    else:
        target_metric = 'cutoff_frequency'
    
    return DesignGoal(
        circuit_type=circuit_type,
        target_metric=target_metric,
        target_value=target_value,
        target_unit=target_unit,
        tolerance=0.05  # 默认5%容差
    )


def _extract_frequency(user_request: str) -> tuple[float, str]:
    """
    从用户请求中提取频率值和单位
    """
    import re
    
    # 查找频率模式：数字 + 单位
    freq_patterns = [
        r'(\d+(?:\.\d+)?)\s*(khz|kHz|KHz)',
        r'(\d+(?:\.\d+)?)\s*(hz|Hz|HZ)',
        r'(\d+(?:\.\d+)?)\s*(mhz|MHz|MHZ)',
        r'(\d+(?:\.\d+)?)\s*k(?:hz|Hz)?',  # 简写形式如 "1k"
        r'(\d+(?:\.\d+)?)\s*(?:hz|Hz|HZ)',
    ]
    
    for pattern in freq_patterns:
        match = re.search(pattern, user_request)
        if match:
            value = float(match.group(1))
            unit = match.group(2) if len(match.groups()) > 1 else 'Hz'
            
            # 标准化单位
            if unit.lower() in ['khz', 'k']:
                return value * 1000, 'Hz'
            elif unit.lower() == 'mhz':
                return value * 1000000, 'Hz'
            else:
                return value, 'Hz'
    
    # 如果没有找到频率，返回默认值
    return 1000.0, 'Hz'


def _create_simulation_params(design_goal: DesignGoal) -> SimulationParameters:
    """
    根据设计目标创建仿真参数
    """
    if design_goal.target_metric == 'cutoff_frequency':
        # 对于截止频率分析，设置合适的频率范围
        target_freq = design_goal.target_value
        start_freq = target_freq / 100  # 从目标频率的1/100开始
        stop_freq = target_freq * 100   # 到目标频率的100倍结束
        
        return SimulationParameters(
            analysis_type='ac',
            start_frequency=start_freq,
            stop_frequency=stop_freq,
            number_of_points=100,
            output_node='out'
        )
    else:
        # 默认AC分析参数
        return SimulationParameters(
            analysis_type='ac',
            start_frequency=1.0,
            stop_frequency=1e6,
            number_of_points=100,
            output_node='out'
        )


# 以下是使用LLM的示例代码（需要配置LLM客户端）
def _parse_user_request_with_llm(user_request: str) -> DesignGoal:
    """
    使用LLM解析用户请求的示例实现
    
    注意：这需要配置LLM客户端（如OpenAI、Anthropic等）
    """
    system_prompt = """
    You are an expert electronics engineer and project manager. Your task is to analyze a user's request for a circuit design and convert it into a structured, machine-readable design goal.

    Instructions:
    1. Carefully read the user's request.
    2. Identify the fundamental type of circuit required (e.g., low-pass RC filter).
    3. Identify the primary performance metric the user cares about (e.g., cutoff frequency).
    4. Extract the target value and its unit for that metric.
    5. If the user does not provide enough information, make reasonable assumptions.
    6. Your final output MUST be a single JSON object that strictly conforms to the DesignGoal schema.

    DesignGoal Schema:
    {
        "circuit_type": "low_pass_rc" | "high_pass_rc" | "band_pass_rc",
        "target_metric": "cutoff_frequency" | "gain" | "phase_margin",
        "target_value": number,
        "target_unit": string,
        "tolerance": number (default: 0.05)
    }
    """
    
    user_prompt = f"User request: {user_request}"
    
    # 这里应该调用实际的LLM API
    # response = llm_client.chat.completions.create(
    #     model="gpt-4",
    #     messages=[
    #         {"role": "system", "content": system_prompt},
    #         {"role": "user", "content": user_prompt}
    #     ]
    # )
    # 
    # result_json = response.choices[0].message.content
    # return DesignGoal.parse_raw(result_json)
    
    # 暂时返回默认值
    return DesignGoal(
        circuit_type='low_pass_rc',
        target_metric='cutoff_frequency',
        target_value=1000.0,
        target_unit='Hz',
        tolerance=0.05
    )
