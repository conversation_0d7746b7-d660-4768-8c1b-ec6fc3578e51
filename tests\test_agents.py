"""
智能体测试

测试各个智能体节点的功能
"""

import unittest
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.circuit_agent.schemas.models import (
    AppState, DesignGoal, CircuitModel, CircuitComponent, 
    SimulationParameters, SimulationResult, Critique
)
from src.circuit_agent.agents.planner import planner_node
from src.circuit_agent.agents.designer import circuit_designer_node
from src.circuit_agent.agents.simulator import simulation_node
from src.circuit_agent.agents.critique import critique_node


class TestPlannerAgent(unittest.TestCase):
    """测试PlannerAgent"""
    
    def test_planner_with_lowpass_request(self):
        """测试低通滤波器请求解析"""
        state = AppState(
            user_request="I need a low-pass filter with cutoff frequency of 1kHz",
            design_goal=None,
            circuit_model=None,
            simulation_params=None,
            simulation_results=None,
            critique_history=[],
            iteration_count=0
        )
        
        result = planner_node(state)
        
        self.assertIn('design_goal', result)
        self.assertIn('simulation_params', result)
        
        design_goal = result['design_goal']
        self.assertEqual(design_goal.circuit_type, 'low_pass_rc')
        self.assertEqual(design_goal.target_metric, 'cutoff_frequency')
        self.assertEqual(design_goal.target_value, 1000.0)
        self.assertEqual(design_goal.target_unit, 'Hz')
    
    def test_planner_with_chinese_request(self):
        """测试中文请求解析"""
        state = AppState(
            user_request="我需要一个2kHz的低通滤波器",
            design_goal=None,
            circuit_model=None,
            simulation_params=None,
            simulation_results=None,
            critique_history=[],
            iteration_count=0
        )
        
        result = planner_node(state)
        
        self.assertIn('design_goal', result)
        design_goal = result['design_goal']
        self.assertEqual(design_goal.circuit_type, 'low_pass_rc')
        self.assertEqual(design_goal.target_value, 2000.0)


class TestCircuitDesignerAgent(unittest.TestCase):
    """测试CircuitDesignerAgent"""
    
    def test_initial_lowpass_design(self):
        """测试初始低通滤波器设计"""
        design_goal = DesignGoal(
            circuit_type='low_pass_rc',
            target_metric='cutoff_frequency',
            target_value=1000.0,
            target_unit='Hz',
            tolerance=0.05
        )
        
        state = AppState(
            user_request="test",
            design_goal=design_goal,
            circuit_model=None,
            simulation_params=None,
            simulation_results=None,
            critique_history=[],
            iteration_count=0
        )
        
        result = circuit_designer_node(state)
        
        self.assertIn('circuit_model', result)
        self.assertIn('iteration_count', result)
        
        circuit_model = result['circuit_model']
        self.assertEqual(circuit_model.title, "Low-Pass RC Filter")
        self.assertEqual(len(circuit_model.components), 2)
        
        # 检查是否有电阻和电容
        component_types = [comp.type for comp in circuit_model.components]
        self.assertIn('R', component_types)
        self.assertIn('C', component_types)
    
    def test_circuit_modification(self):
        """测试电路修改"""
        design_goal = DesignGoal(
            circuit_type='low_pass_rc',
            target_metric='cutoff_frequency',
            target_value=1000.0,
            target_unit='Hz',
            tolerance=0.05
        )
        
        current_circuit = CircuitModel(
            title="Test Circuit",
            components=[
                CircuitComponent(name='R1', type='R', nodes=('in', 'out'), value=1000.0, unit='Ohm'),
                CircuitComponent(name='C1', type='C', nodes=('out', 'gnd'), value=159.0, unit='nF')
            ]
        )
        
        critique = Critique(
            goal_met=False,
            feedback="Cutoff frequency is too high",
            metrics={'cutoff_frequency': 1500.0}
        )
        
        state = AppState(
            user_request="test",
            design_goal=design_goal,
            circuit_model=current_circuit,
            simulation_params=None,
            simulation_results=None,
            critique_history=[critique],
            iteration_count=1
        )
        
        result = circuit_designer_node(state)
        
        self.assertIn('circuit_model', result)
        modified_circuit = result['circuit_model']
        self.assertEqual(len(modified_circuit.components), 2)


class TestSimulationAgent(unittest.TestCase):
    """测试SimulationAgent"""
    
    def test_simulation_with_valid_circuit(self):
        """测试有效电路的仿真"""
        circuit_model = CircuitModel(
            title="Test Circuit",
            components=[
                CircuitComponent(name='R1', type='R', nodes=('in', 'out'), value=1000.0, unit='Ohm'),
                CircuitComponent(name='C1', type='C', nodes=('out', 'gnd'), value=159.0, unit='nF')
            ]
        )
        
        simulation_params = SimulationParameters(
            analysis_type='ac',
            start_frequency=1.0,
            stop_frequency=1e6,
            number_of_points=100,
            output_node='out'
        )
        
        state = AppState(
            user_request="test",
            design_goal=None,
            circuit_model=circuit_model,
            simulation_params=simulation_params,
            simulation_results=None,
            critique_history=[],
            iteration_count=0
        )
        
        result = simulation_node(state)
        
        self.assertIn('simulation_results', result)
        sim_result = result['simulation_results']
        self.assertEqual(sim_result.analysis_type, 'ac')
        
        # 由于使用模拟数据，应该有数据返回
        if sim_result.error is None:
            self.assertIsNotNone(sim_result.data)
            self.assertIn('frequency', sim_result.data)
            self.assertIn('vout', sim_result.data)


class TestCritiqueAgent(unittest.TestCase):
    """测试CritiqueAgent"""
    
    def test_critique_with_simulation_results(self):
        """测试仿真结果评估"""
        import numpy as np
        
        design_goal = DesignGoal(
            circuit_type='low_pass_rc',
            target_metric='cutoff_frequency',
            target_value=1000.0,
            target_unit='Hz',
            tolerance=0.05
        )
        
        # 创建模拟仿真数据
        frequencies = np.logspace(1, 6, 100)
        cutoff_freq = 1000.0
        vout_magnitude = 1.0 / np.sqrt(1 + (frequencies / cutoff_freq) ** 2)
        vout_phase = -np.arctan(frequencies / cutoff_freq)
        vout_complex = vout_magnitude * np.exp(1j * vout_phase)
        
        simulation_result = SimulationResult(
            analysis_type='ac',
            data={'frequency': frequencies, 'vout': vout_complex},
            vectors=['frequency', 'vout'],
            error=None
        )
        
        state = AppState(
            user_request="test",
            design_goal=design_goal,
            circuit_model=None,
            simulation_params=None,
            simulation_results=simulation_result,
            critique_history=[],
            iteration_count=0
        )
        
        result = critique_node(state)
        
        self.assertIn('critique_history', result)
        critique_list = result['critique_history']
        self.assertEqual(len(critique_list), 1)
        
        critique = critique_list[0]
        self.assertIsInstance(critique.goal_met, bool)
        self.assertIsInstance(critique.feedback, str)
        self.assertIsInstance(critique.metrics, dict)
        self.assertIn('cutoff_frequency', critique.metrics)


if __name__ == '__main__':
    unittest.main()
