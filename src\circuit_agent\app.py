"""
LangGraph应用构建

组装和编译LangGraph工作流。
定义图的节点、边和条件路由逻辑。
"""

import logging
from typing import Dict, Any

try:
    from langgraph.graph import StateGraph, END
    from langgraph.checkpoint.memory import MemorySaver
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False
    logging.warning("LangGraph not available. Using mock implementation.")

from .schemas.models import AppState
from .agents.planner import planner_node
from .agents.designer import circuit_designer_node
from .agents.simulator import simulation_node
from .agents.critique import critique_node

# --- 1. 定义常量和路由函数 ---
MAX_ITERATIONS = 10


def after_critique_router(state: AppState) -> str:
    """
    在Critique节点之后决定工作流的走向。
    - 如果目标达成或达到最大迭代次数，则结束。
    - 否则，返回到Designer节点进行下一次迭代。
    """
    logger = logging.getLogger(__name__)
    
    # 检查迭代次数是否超限
    iteration_count = state.get("iteration_count", 0)
    if iteration_count >= MAX_ITERATIONS:
        logger.info(f"Max iterations ({MAX_ITERATIONS}) reached. Ending.")
        return END
    
    # 检查是否有评估历史
    critique_history = state.get("critique_history", [])
    if not critique_history:
        logger.warning("No critique history found. Ending.")
        return END
    
    # 检查最新评估结果
    last_critique = critique_history[-1]
    if last_critique.goal_met:
        logger.info("Design goal achieved. Ending.")
        return END
    else:
        logger.info("Goal not met. Routing back to designer for iteration.")
        return "designer"


def after_simulator_router(state: AppState) -> str:
    """
    在Simulator节点之后决定工作流的走向。
    - 如果仿真失败，返回Designer节点进行修正。
    - 如果仿真成功，进入Critique节点进行评估。
    """
    logger = logging.getLogger(__name__)
    
    simulation_results = state.get("simulation_results")
    if not simulation_results:
        logger.error("No simulation results found. Routing to designer.")
        return "designer"
    
    if simulation_results.error:
        logger.warning(f"Simulation failed: {simulation_results.error}. Routing to designer.")
        return "designer"
    else:
        logger.info("Simulation successful. Routing to critique.")
        return "critique"


def after_designer_router(state: AppState) -> str:
    """
    在Designer节点之后决定工作流的走向。
    总是路由到Simulator节点。
    """
    return "simulator"


def after_planner_router(state: AppState) -> str:
    """
    在Planner节点之后决定工作流的走向。
    总是路由到Designer节点。
    """
    return "designer"


# --- 2. 构建图 ---
def build_graph():
    """
    构建、连接并编译LangGraph状态机。
    """
    if not LANGGRAPH_AVAILABLE:
        logging.warning("LangGraph not available. Returning mock app.")
        return MockApp()
    
    logger = logging.getLogger(__name__)
    logger.info("Building LangGraph workflow...")
    
    # 初始化StateGraph
    workflow_builder = StateGraph(AppState)
    
    # 添加节点
    workflow_builder.add_node("planner", planner_node)
    workflow_builder.add_node("designer", circuit_designer_node)
    workflow_builder.add_node("simulator", simulation_node)
    workflow_builder.add_node("critique", critique_node)
    
    # 设置入口点
    workflow_builder.set_entry_point("planner")
    
    # 添加条件边
    workflow_builder.add_conditional_edges(
        "planner",
        after_planner_router,
        {"designer": "designer"}
    )
    
    workflow_builder.add_conditional_edges(
        "designer",
        after_designer_router,
        {"simulator": "simulator"}
    )
    
    workflow_builder.add_conditional_edges(
        "simulator",
        after_simulator_router,
        {"designer": "designer", "critique": "critique"}
    )
    
    workflow_builder.add_conditional_edges(
        "critique",
        after_critique_router,
        {"designer": "designer", END: END}
    )
    
    # 编译图
    memory = MemorySaver()
    app = workflow_builder.compile(checkpointer=memory)
    
    logger.info("LangGraph workflow built successfully.")
    return app


class MockApp:
    """
    当LangGraph不可用时的模拟应用
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.warning("Using mock LangGraph app")
    
    def stream(self, initial_state: Dict[str, Any], config: Dict[str, Any], stream_mode: str = "values"):
        """
        模拟LangGraph的stream方法
        """
        self.logger.info("Running mock workflow...")
        
        # 模拟工作流执行
        state = initial_state.copy()
        
        # 步骤1: Planner
        yield {"planner": planner_node(state)}
        state.update(planner_node(state))
        
        # 步骤2: Designer
        yield {"designer": circuit_designer_node(state)}
        state.update(circuit_designer_node(state))
        
        # 步骤3: Simulator
        yield {"simulator": simulation_node(state)}
        state.update(simulation_node(state))
        
        # 步骤4: Critique
        yield {"critique": critique_node(state)}
        state.update(critique_node(state))
        
        self.logger.info("Mock workflow completed")
    
    def get_state(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        模拟获取状态
        """
        return {}


# 在模块加载时编译应用
app = build_graph()


def get_app():
    """
    获取编译后的LangGraph应用实例
    """
    return app


def create_initial_state(user_request: str) -> AppState:
    """
    创建初始状态
    
    Args:
        user_request: 用户请求字符串
        
    Returns:
        初始化的AppState
    """
    return AppState(
        user_request=user_request,
        design_goal=None,
        circuit_model=None,
        simulation_params=None,
        simulation_results=None,
        critique_history=[],
        iteration_count=0
    )


def run_workflow(user_request: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    运行完整的工作流
    
    Args:
        user_request: 用户请求
        config: 配置字典
        
    Returns:
        最终状态
    """
    if config is None:
        config = {"configurable": {"thread_id": "default"}}
    
    initial_state = create_initial_state(user_request)
    
    final_state = None
    for event in app.stream(initial_state, config, stream_mode="values"):
        final_state = event
    
    return final_state
