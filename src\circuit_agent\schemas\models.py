"""
数据模型定义

定义项目中所有的数据结构和模式 (Pydantic Models)。
这些模型是智能体之间以及智能体与工具之间沟通的契约。
"""

from typing import Literal, Optional, List, Tuple, Any, TypedDict, Annotated
from pydantic import BaseModel, Field
import operator
import numpy as np


class DesignGoal(BaseModel):
    """
    结构化的电路设计目标，由PlannerAgent生成。
    """
    circuit_type: Literal['low_pass_rc', 'high_pass_rc', 'band_pass_rc'] = Field(
       ..., description="目标电路的类型, 例如 'low_pass_rc'."
    )
    target_metric: Literal['cutoff_frequency', 'gain', 'phase_margin'] = Field(
       ..., description="核心优化指标, 例如 'cutoff_frequency'."
    )
    target_value: float = Field(..., description="目标指标的数值。")
    target_unit: str = Field(..., description="目标指标的单位, 例如 'Hz', 'dB'。")
    tolerance: Optional[float] = Field(0.05, description="目标值的可接受公差范围, 例如 0.05 表示 ±5%。")


class CircuitComponent(BaseModel):
    """
    表示电路中的单个元件。
    """
    name: str = Field(..., description="元件的唯一标识符, 例如 'R1', 'C1'。")
    type: Literal['R', 'C', 'V', 'L'] = Field(..., description="元件类型 (Resistor, Capacitor, Voltage Source, Inductor)。")
    nodes: Tuple[str, str] = Field(..., description="元件连接的两个节点, 例如 ('in', 'out')。")
    value: float = Field(..., description="元件的数值 (单位在生成网表时指定)。")
    unit: str = Field(..., description="元件值的单位, 例如 'kOhm', 'nF', 'V'。")


class CircuitModel(BaseModel):
    """
    表示完整的电路模型，由CircuitDesignerAgent生成或修改。
    """
    title: str = Field("AI Generated Circuit", description="电路的标题。")
    components: List[CircuitComponent] = Field(..., description="构成电路的元件列表。")


class SimulationParameters(BaseModel):
    """
    定义仿真任务的参数。
    """
    analysis_type: Literal['ac', 'tran', 'op'] = Field(..., description="要执行的仿真分析类型。")
    
    # AC 分析参数
    start_frequency: Optional[float] = Field(1.0, description="AC分析的起始频率 (Hz)。")
    stop_frequency: Optional[float] = Field(1e6, description="AC分析的终止频率 (Hz)。")
    number_of_points: Optional[int] = Field(100, description="AC分析中每个十倍频的点数。")
    output_node: str = Field('out', description="AC分析中用于测量输出的节点名称。")


class SimulationResult(BaseModel):
    """
    封装单次仿真运行的结果，由SimulationAgent(Tool)返回。
    """
    analysis_type: str = Field(..., description="执行的仿真分析类型。")
    data: Optional[Any] = Field(None, description="仿真成功时返回的数据, 通常是包含NumPy数组的字典。")
    vectors: Optional[List[str]] = Field(None, description="数据中包含的向量名称列表, 例如 ['frequency', 'v(out)']。")
    error: Optional[str] = Field(None, description="仿真失败时返回的错误信息。")

    class Config:
        arbitrary_types_allowed = True


class Critique(BaseModel):
    """
    封装单次评估的结果，由CritiqueAgent生成。
    """
    goal_met: bool = Field(..., description="一个布尔标志，指示设计目标是否已在容差范围内实现。")
    feedback: str = Field(..., description="给设计智能体的简洁文本反馈，说明当前设计的问题和改进建议。")
    metrics: dict = Field(..., description="一个包含从仿真结果中计算出的关键性能指标的字典。")


class AppState(TypedDict):
    """
    定义LangGraph的中心状态 (State)。
    这是整个应用中所有智能体共享的唯一事实来源。
    """
    user_request: str
    design_goal: Optional[DesignGoal]
    circuit_model: Optional[CircuitModel]
    simulation_params: Optional[SimulationParameters]
    simulation_results: Optional[SimulationResult]
    # 使用Annotated和operator.add来指示LangGraph在更新时追加到列表，而不是替换
    critique_history: Annotated[List[Critique], operator.add]
    iteration_count: int
