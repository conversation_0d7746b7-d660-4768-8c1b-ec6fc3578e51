"""
CircuitDesignerAgent实现

实现CircuitDesignerAgent的核心逻辑，根据设计目标和历史评估来生成或修改电路模型。
"""

import logging
import math
from typing import Dict, Any, List

from ..schemas.models import AppState, CircuitModel, CircuitComponent, DesignGoal, Critique


def circuit_designer_node(state: AppState) -> Dict[str, Any]:
    """
    CircuitDesignerAgent在LangGraph中的实现节点。
    此节点调用LLM，根据设计目标(design_goal)和历史评估(critique_history)
    来生成或修改电路模型(circuit_model)。

    Args:
        state: 当前的LangGraph应用状态。

    Returns:
        一个字典，用于更新状态中的 'circuit_model' 和 'iteration_count'。
    """
    logger = logging.getLogger(__name__)
    logger.info("--- Executing Circuit Designer Node ---")
    
    design_goal = state.get('design_goal')
    current_circuit = state.get('circuit_model')
    critique_history = state.get('critique_history', [])
    iteration_count = state.get('iteration_count', 0)
    
    if not design_goal:
        logger.error("No design goal found in state")
        return {}
    
    try:
        if current_circuit is None:
            # 首次设计
            logger.info("Creating initial circuit design")
            circuit_model = _create_initial_circuit(design_goal)
        else:
            # 基于反馈修改电路
            logger.info(f"Modifying circuit based on feedback (iteration {iteration_count})")
            circuit_model = _modify_circuit(current_circuit, design_goal, critique_history)
        
        logger.info(f"Generated circuit: {circuit_model}")
        
        return {
            'circuit_model': circuit_model,
            'iteration_count': iteration_count + 1
        }
        
    except Exception as e:
        logger.error(f"Error in circuit_designer_node: {e}")
        return {}


def _create_initial_circuit(design_goal: DesignGoal) -> CircuitModel:
    """
    根据设计目标创建初始电路设计
    """
    if design_goal.circuit_type == 'low_pass_rc':
        return _create_low_pass_rc_filter(design_goal)
    elif design_goal.circuit_type == 'high_pass_rc':
        return _create_high_pass_rc_filter(design_goal)
    elif design_goal.circuit_type == 'band_pass_rc':
        return _create_band_pass_rc_filter(design_goal)
    else:
        raise ValueError(f"Unsupported circuit type: {design_goal.circuit_type}")


def _create_low_pass_rc_filter(design_goal: DesignGoal) -> CircuitModel:
    """
    创建低通RC滤波器
    
    对于低通RC滤波器：fc = 1 / (2 * π * R * C)
    """
    if design_goal.target_metric == 'cutoff_frequency':
        target_freq = design_goal.target_value
        
        # 选择合适的R和C值
        # 使用常见的电阻值，然后计算对应的电容值
        R_value = 1000.0  # 1kΩ
        C_value = 1.0 / (2 * math.pi * R_value * target_freq)
        
        # 将电容值转换为合适的单位
        if C_value >= 1e-6:
            C_value_display = C_value * 1e6
            C_unit = 'uF'
        elif C_value >= 1e-9:
            C_value_display = C_value * 1e9
            C_unit = 'nF'
        else:
            C_value_display = C_value * 1e12
            C_unit = 'pF'
        
        components = [
            CircuitComponent(
                name='R1',
                type='R',
                nodes=('in', 'out'),
                value=R_value,
                unit='Ohm'
            ),
            CircuitComponent(
                name='C1',
                type='C',
                nodes=('out', 'gnd'),
                value=C_value_display,
                unit=C_unit
            )
        ]
    else:
        # 默认设计
        components = [
            CircuitComponent(name='R1', type='R', nodes=('in', 'out'), value=1000.0, unit='Ohm'),
            CircuitComponent(name='C1', type='C', nodes=('out', 'gnd'), value=159.0, unit='nF')
        ]
    
    return CircuitModel(
        title="Low-Pass RC Filter",
        components=components
    )


def _create_high_pass_rc_filter(design_goal: DesignGoal) -> CircuitModel:
    """
    创建高通RC滤波器
    """
    if design_goal.target_metric == 'cutoff_frequency':
        target_freq = design_goal.target_value
        
        # 对于高通滤波器：fc = 1 / (2 * π * R * C)
        R_value = 1000.0  # 1kΩ
        C_value = 1.0 / (2 * math.pi * R_value * target_freq)
        
        # 转换电容单位
        if C_value >= 1e-6:
            C_value_display = C_value * 1e6
            C_unit = 'uF'
        elif C_value >= 1e-9:
            C_value_display = C_value * 1e9
            C_unit = 'nF'
        else:
            C_value_display = C_value * 1e12
            C_unit = 'pF'
        
        components = [
            CircuitComponent(
                name='C1',
                type='C',
                nodes=('in', 'out'),
                value=C_value_display,
                unit=C_unit
            ),
            CircuitComponent(
                name='R1',
                type='R',
                nodes=('out', 'gnd'),
                value=R_value,
                unit='Ohm'
            )
        ]
    else:
        # 默认设计
        components = [
            CircuitComponent(name='C1', type='C', nodes=('in', 'out'), value=159.0, unit='nF'),
            CircuitComponent(name='R1', type='R', nodes=('out', 'gnd'), value=1000.0, unit='Ohm')
        ]
    
    return CircuitModel(
        title="High-Pass RC Filter",
        components=components
    )


def _create_band_pass_rc_filter(design_goal: DesignGoal) -> CircuitModel:
    """
    创建带通RC滤波器（简化版本）
    """
    # 简化的带通滤波器：高通 + 低通级联
    components = [
        CircuitComponent(name='C1', type='C', nodes=('in', 'mid'), value=100.0, unit='nF'),
        CircuitComponent(name='R1', type='R', nodes=('mid', 'gnd'), value=1000.0, unit='Ohm'),
        CircuitComponent(name='R2', type='R', nodes=('mid', 'out'), value=1000.0, unit='Ohm'),
        CircuitComponent(name='C2', type='C', nodes=('out', 'gnd'), value=100.0, unit='nF')
    ]
    
    return CircuitModel(
        title="Band-Pass RC Filter",
        components=components
    )


def _modify_circuit(
    current_circuit: CircuitModel,
    design_goal: DesignGoal,
    critique_history: List[Critique]
) -> CircuitModel:
    """
    基于反馈修改现有电路
    """
    if not critique_history:
        return current_circuit
    
    last_critique = critique_history[-1]
    
    # 分析最新的反馈
    feedback = last_critique.feedback.lower()
    metrics = last_critique.metrics
    
    # 创建修改后的电路
    modified_components = []
    
    for comp in current_circuit.components:
        new_comp = CircuitComponent(
            name=comp.name,
            type=comp.type,
            nodes=comp.nodes,
            value=comp.value,
            unit=comp.unit
        )
        
        # 根据反馈调整元件值
        if design_goal.target_metric == 'cutoff_frequency':
            actual_freq = metrics.get('cutoff_frequency', 0)
            target_freq = design_goal.target_value
            
            if actual_freq > target_freq:
                # 实际频率太高，需要降低
                if comp.type == 'R' and design_goal.circuit_type == 'low_pass_rc':
                    # 增加电阻值
                    new_comp.value = comp.value * 1.5
                elif comp.type == 'C':
                    # 增加电容值
                    new_comp.value = comp.value * 1.5
            elif actual_freq < target_freq:
                # 实际频率太低，需要提高
                if comp.type == 'R' and design_goal.circuit_type == 'low_pass_rc':
                    # 减少电阻值
                    new_comp.value = comp.value * 0.7
                elif comp.type == 'C':
                    # 减少电容值
                    new_comp.value = comp.value * 0.7
        
        modified_components.append(new_comp)
    
    return CircuitModel(
        title=current_circuit.title,
        components=modified_components
    )


# 以下是使用LLM的示例代码
def _design_circuit_with_llm(design_goal: DesignGoal, critique_history: List[Critique]) -> CircuitModel:
    """
    使用LLM设计电路的示例实现
    
    注意：这需要配置LLM客户端
    """
    system_prompt = """
    You are a highly skilled circuit design specialist. Your task is to generate or modify a circuit to meet a specific design goal, taking into account feedback from previous simulation attempts.

    Instructions:
    1. Analyze the Design Goal.
    2. Review the Most Recent Critique.
    3. Consult the Critique History to avoid repeating past mistakes.
    4. Propose a modification to the Current Circuit Model by only modifying the value of the components.
    5. Your output MUST be a single JSON object that strictly conforms to the CircuitModel schema.

    CircuitModel Schema:
    {
        "title": string,
        "components": [
            {
                "name": string,
                "type": "R" | "C" | "V" | "L",
                "nodes": [string, string],
                "value": number,
                "unit": string
            }
        ]
    }
    """
    
    # 这里应该调用实际的LLM API
    # 暂时返回默认电路
    return _create_low_pass_rc_filter(design_goal)
