```mermaid
graph LR
    A[Start] --> B[User Input]
    B --> C[Planner Node]
    C --> D[Design Node]
    D --> E[Simulation Node]
    E --> F[Critique Node]
    F --> G[End]
```

```mermaid
graph TD
    %% 定义节点
    A[用户请求]
    B[PlannerAgent]
    C{Supervisor 决策}
    D[CircuitDesignerAgent]
    E[SimulationAgent]
    F[CritiqueAgent]
    G[结果]
    
    %% 定义连接关系
    A --> B
    B --> C
    C -- 设计目标已定义 --> D
    D -- 网表已生成/修改 --> C
    C -- 网表待仿真 --> E
    E -- 仿真完成 --> C
    C -- 结果待评估 --> F
    F -- 评估完成, 未达标 --> D
    F -- 评估完成, 已达标 --> G
    C -- 其他条件 --> G
```