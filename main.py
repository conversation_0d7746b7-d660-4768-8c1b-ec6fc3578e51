"""
主应用入口

应用的主入口点。
处理用户命令行交互，并驱动LangGraph应用执行。
"""

import uuid
import logging
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.circuit_agent.app import get_app, create_initial_state
from src.circuit_agent.schemas.models import AppState


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('circuit_agent.log')
        ]
    )


def print_welcome():
    """打印欢迎信息"""
    print("=" * 60)
    print("🔌 AI自动化电路设计工具 - LangSpice v2")
    print("=" * 60)
    print("基于LangGraph和ngspice的智能电路设计系统")
    print()
    print("支持的电路类型:")
    print("  • 低通RC滤波器 (low-pass RC filter)")
    print("  • 高通RC滤波器 (high-pass RC filter)")
    print("  • 带通RC滤波器 (band-pass RC filter)")
    print()
    print("示例请求:")
    print("  • 我需要一个1kHz的低通滤波器")
    print("  • 设计一个截止频率为500Hz的高通滤波器")
    print("  • I need a low-pass filter with cutoff frequency of 2kHz")
    print()
    print("输入 'quit' 或 'exit' 退出程序")
    print("=" * 60)
    print()


def print_results(final_state):
    """打印最终结果"""
    if not final_state:
        print("❌ 工作流执行失败")
        return

    print("\n" + "=" * 50)
    print("📊 设计结果")
    print("=" * 50)

    # 打印设计目标
    design_goal = final_state.get('design_goal')
    if design_goal:
        print(f"🎯 设计目标: {design_goal.circuit_type}")
        print(f"📏 目标指标: {design_goal.target_metric} = {design_goal.target_value} {design_goal.target_unit}")
        print(f"🎚️  容差范围: ±{design_goal.tolerance*100:.1f}%")

    # 打印电路模型
    circuit_model = final_state.get('circuit_model')
    if circuit_model:
        print(f"\n🔧 电路设计: {circuit_model.title}")
        print("📋 元件列表:")
        for comp in circuit_model.components:
            print(f"   {comp.name}: {comp.value} {comp.unit} ({comp.type}) - 节点: {comp.nodes}")

    # 打印评估结果
    critique_history = final_state.get('critique_history', [])
    if critique_history:
        last_critique = critique_history[-1]
        status = "✅ 成功" if last_critique.goal_met else "❌ 未达标"
        print(f"\n📈 评估结果: {status}")
        print(f"💬 反馈: {last_critique.feedback}")

        if last_critique.metrics:
            print("📊 性能指标:")
            for key, value in last_critique.metrics.items():
                if isinstance(value, (int, float)):
                    print(f"   {key}: {value:.2f}")
                else:
                    print(f"   {key}: {value}")

    # 打印迭代信息
    iteration_count = final_state.get('iteration_count', 0)
    print(f"\n🔄 迭代次数: {iteration_count}")

    print("=" * 50)


def main():
    """
    主应用函数，处理用户交互和图的执行。
    """
    setup_logging()
    logger = logging.getLogger(__name__)

    print_welcome()

    # 获取LangGraph应用
    app = get_app()

    while True:
        try:
            user_input = input("🤖 请输入您的电路设计需求: ").strip()

            if user_input.lower() in ["quit", "exit", "退出"]:
                print("👋 感谢使用AI电路设计工具，再见！")
                break

            if not user_input:
                print("⚠️  请输入有效的设计需求")
                continue

            print(f"\n🚀 开始处理您的请求: {user_input}")
            print("⏳ 正在执行设计流程...")

            # 创建唯一的线程ID
            thread_id = str(uuid.uuid4())
            config = {"configurable": {"thread_id": thread_id}}

            # 创建初始状态
            initial_state = create_initial_state(user_input)

            # 执行工作流
            final_state = None
            step_count = 0

            try:
                for event in app.stream(initial_state, config, stream_mode="values"):
                    step_count += 1
                    for key, value in event.items():
                        if key in ['planner', 'designer', 'simulator', 'critique']:
                            print(f"  📍 步骤 {step_count}: {key} 节点执行完成")
                    final_state = event

                # 打印结果
                print_results(final_state)

            except Exception as e:
                logger.error(f"工作流执行错误: {e}")
                print(f"❌ 执行过程中发生错误: {e}")
                print("💡 请检查您的输入或稍后重试")

            print("\n" + "-" * 60 + "\n")

        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            logger.error(f"主循环错误: {e}")
            print(f"❌ 发生意外错误: {e}")
            print("💡 程序将继续运行，请重新输入")


if __name__ == "__main__":
    main()
