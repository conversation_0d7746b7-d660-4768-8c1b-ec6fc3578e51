"""
工具测试

测试NgspiceSimulator等工具的功能
"""

import unittest
import sys
import os
import numpy as np

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.circuit_agent.schemas.models import CircuitModel, CircuitComponent, SimulationParameters
from src.circuit_agent.tools.ngspice_simulator import NgspiceSimulator


class TestNgspiceSimulator(unittest.TestCase):
    """测试NgspiceSimulator工具"""
    
    def setUp(self):
        """设置测试环境"""
        self.simulator = NgspiceSimulator()
    
    def test_simulator_initialization(self):
        """测试仿真器初始化"""
        self.assertIsNotNone(self.simulator)
    
    def test_unit_multiplier(self):
        """测试单位乘数计算"""
        self.assertEqual(self.simulator._get_unit_multiplier('Ohm'), 1.0)
        self.assertEqual(self.simulator._get_unit_multiplier('kOhm'), 1e3)
        self.assertEqual(self.simulator._get_unit_multiplier('nF'), 1e-9)
        self.assertEqual(self.simulator._get_unit_multiplier('Hz'), 1.0)
        self.assertEqual(self.simulator._get_unit_multiplier('kHz'), 1e3)
    
    def test_ac_analysis_with_simple_circuit(self):
        """测试简单电路的AC分析"""
        # 创建简单的RC低通滤波器
        circuit_model = CircuitModel(
            title="Test RC Filter",
            components=[
                CircuitComponent(
                    name='R1',
                    type='R',
                    nodes=('in', 'out'),
                    value=1000.0,
                    unit='Ohm'
                ),
                CircuitComponent(
                    name='C1',
                    type='C',
                    nodes=('out', 'gnd'),
                    value=159.0,
                    unit='nF'
                )
            ]
        )
        
        simulation_params = SimulationParameters(
            analysis_type='ac',
            start_frequency=1.0,
            stop_frequency=1e6,
            number_of_points=100,
            output_node='out'
        )
        
        result = self.simulator.run_ac_analysis(circuit_model, simulation_params)
        
        # 检查结果结构
        self.assertEqual(result.analysis_type, 'ac')
        
        # 如果没有错误，应该有数据
        if result.error is None:
            self.assertIsNotNone(result.data)
            self.assertIn('frequency', result.data)
            self.assertIn('vout', result.data)
            self.assertEqual(result.vectors, ['frequency', 'vout'])
            
            # 检查数据类型和长度
            freq_data = result.data['frequency']
            vout_data = result.data['vout']
            
            self.assertIsInstance(freq_data, np.ndarray)
            self.assertIsInstance(vout_data, np.ndarray)
            self.assertEqual(len(freq_data), len(vout_data))
            self.assertGreater(len(freq_data), 0)
    
    def test_ac_analysis_with_invalid_circuit(self):
        """测试无效电路的AC分析"""
        # 创建无效电路（空组件列表）
        circuit_model = CircuitModel(
            title="Invalid Circuit",
            components=[]
        )
        
        simulation_params = SimulationParameters(
            analysis_type='ac',
            start_frequency=1.0,
            stop_frequency=1e6,
            number_of_points=100,
            output_node='out'
        )
        
        result = self.simulator.run_ac_analysis(circuit_model, simulation_params)
        
        # 应该返回错误
        self.assertIsNotNone(result.error)
        self.assertIsNone(result.data)
    
    def test_mock_ac_analysis(self):
        """测试模拟AC分析"""
        simulation_params = SimulationParameters(
            analysis_type='ac',
            start_frequency=10.0,
            stop_frequency=10000.0,
            number_of_points=50,
            output_node='out'
        )
        
        mock_data = self.simulator._mock_ac_analysis(simulation_params)
        
        self.assertIn('frequency', mock_data)
        self.assertIn('vout', mock_data)
        
        freq_data = mock_data['frequency']
        vout_data = mock_data['vout']
        
        self.assertEqual(len(freq_data), 50)
        self.assertEqual(len(vout_data), 50)
        
        # 检查频率范围
        self.assertAlmostEqual(freq_data[0], 10.0, places=1)
        self.assertAlmostEqual(freq_data[-1], 10000.0, places=1)
        
        # 检查输出是复数
        self.assertTrue(np.iscomplexobj(vout_data))
    
    def test_unsupported_analysis_types(self):
        """测试不支持的分析类型"""
        circuit_model = CircuitModel(
            title="Test Circuit",
            components=[
                CircuitComponent(name='R1', type='R', nodes=('in', 'out'), value=1000.0, unit='Ohm')
            ]
        )
        
        simulation_params = SimulationParameters(
            analysis_type='tran',
            start_frequency=1.0,
            stop_frequency=1e6,
            number_of_points=100,
            output_node='out'
        )
        
        # 测试瞬态分析（未实现）
        result = self.simulator.run_transient_analysis(circuit_model, simulation_params)
        self.assertIsNotNone(result.error)
        self.assertIn("not implemented", result.error)
        
        # 测试直流工作点分析（未实现）
        simulation_params.analysis_type = 'op'
        result = self.simulator.run_dc_operating_point(circuit_model, simulation_params)
        self.assertIsNotNone(result.error)
        self.assertIn("not implemented", result.error)


class TestCircuitValidation(unittest.TestCase):
    """测试电路验证功能"""
    
    def test_valid_circuit(self):
        """测试有效电路"""
        from src.circuit_agent.agents.simulator import validate_circuit_model
        
        circuit_model = CircuitModel(
            title="Valid Circuit",
            components=[
                CircuitComponent(name='R1', type='R', nodes=('in', 'out'), value=1000.0, unit='Ohm'),
                CircuitComponent(name='C1', type='C', nodes=('out', 'gnd'), value=100.0, unit='nF')
            ]
        )
        
        self.assertTrue(validate_circuit_model(circuit_model))
    
    def test_invalid_circuit(self):
        """测试无效电路"""
        from src.circuit_agent.agents.simulator import validate_circuit_model
        
        # 测试None
        self.assertFalse(validate_circuit_model(None))
        
        # 测试空组件列表
        circuit_model = CircuitModel(title="Empty Circuit", components=[])
        self.assertFalse(validate_circuit_model(circuit_model))
    
    def test_simulation_params_validation(self):
        """测试仿真参数验证"""
        from src.circuit_agent.agents.simulator import validate_simulation_params
        
        # 有效参数
        valid_params = SimulationParameters(
            analysis_type='ac',
            start_frequency=1.0,
            stop_frequency=1e6,
            number_of_points=100,
            output_node='out'
        )
        self.assertTrue(validate_simulation_params(valid_params))
        
        # 无效参数 - None
        self.assertFalse(validate_simulation_params(None))
        
        # 无效参数 - 错误的分析类型
        invalid_params = SimulationParameters(
            analysis_type='invalid',
            start_frequency=1.0,
            stop_frequency=1e6,
            number_of_points=100,
            output_node='out'
        )
        self.assertFalse(validate_simulation_params(invalid_params))


if __name__ == '__main__':
    unittest.main()
