"""
CritiqueAgent实现

实现CritiqueAgent的核心逻辑和其使用的确定性计算函数。
结合了计算机精确的计算能力和LLM卓越的语言综合能力。
"""

import logging
import numpy as np
from typing import Dict, Any, List

from ..schemas.models import AppState, Critique, DesignGoal, SimulationResult


def critique_node(state: AppState) -> Dict[str, Any]:
    """
    CritiqueAgent在LangGraph中的实现节点。
    此节点执行两步操作：
    1. 调用确定性函数（如 calculate_cutoff_frequency）从仿真结果中提取关键性能指标(KPIs)。
    2. 调用LLM，将提取的KPIs与设计目标进行比较，生成结构化的Critique对象。

    Args:
        state: 当前的LangGraph应用状态。

    Returns:
        一个字典，用于更新状态中的 'critique_history'。
    """
    logger = logging.getLogger(__name__)
    logger.info("--- Executing Critique Node ---")
    
    design_goal = state.get('design_goal')
    simulation_results = state.get('simulation_results')
    
    if not design_goal:
        logger.error("No design goal found in state")
        return {}
    
    if not simulation_results:
        logger.error("No simulation results found in state")
        return {}
    
    if simulation_results.error:
        logger.error(f"Simulation failed: {simulation_results.error}")
        # 为仿真失败创建critique
        critique = Critique(
            goal_met=False,
            feedback=f"Simulation failed: {simulation_results.error}. Please check the circuit design and try again.",
            metrics={"error": simulation_results.error}
        )
        return {'critique_history': [critique]}
    
    try:
        # 步骤1：从仿真结果中提取关键性能指标
        metrics = _extract_performance_metrics(simulation_results, design_goal)
        logger.info(f"Extracted metrics: {metrics}")
        
        # 步骤2：评估是否达到设计目标
        goal_met, feedback = _evaluate_design_goal(design_goal, metrics)
        logger.info(f"Goal met: {goal_met}")
        logger.info(f"Feedback: {feedback}")
        
        # 创建Critique对象
        critique = Critique(
            goal_met=goal_met,
            feedback=feedback,
            metrics=metrics
        )
        
        return {'critique_history': [critique]}
        
    except Exception as e:
        logger.error(f"Error in critique_node: {e}")
        critique = Critique(
            goal_met=False,
            feedback=f"Error during evaluation: {str(e)}",
            metrics={"error": str(e)}
        )
        return {'critique_history': [critique]}


def calculate_cutoff_frequency(freq_array: np.ndarray, vout_array: np.ndarray) -> float:
    """
    从AC分析结果中计算-3dB截止频率。

    Args:
        freq_array: 频率点的NumPy数组。
        vout_array: 对应频率的复数电压输出NumPy数组。

    Returns:
        计算出的截止频率值 (Hz)。
    """
    try:
        # 计算幅度（dB）
        magnitude_db = 20 * np.log10(np.abs(vout_array))
        
        # 找到通带增益（低频处的增益）
        passband_gain = np.max(magnitude_db[:len(magnitude_db)//10])  # 取前10%的最大值
        
        # 计算-3dB点
        target_gain = passband_gain - 3.0
        
        # 找到最接近-3dB点的频率
        diff = np.abs(magnitude_db - target_gain)
        cutoff_index = np.argmin(diff)
        
        # 使用插值获得更精确的截止频率
        if cutoff_index > 0 and cutoff_index < len(freq_array) - 1:
            # 线性插值
            f1, f2 = freq_array[cutoff_index-1], freq_array[cutoff_index+1]
            g1, g2 = magnitude_db[cutoff_index-1], magnitude_db[cutoff_index+1]
            
            if g2 != g1:  # 避免除零
                cutoff_freq = f1 + (target_gain - g1) * (f2 - f1) / (g2 - g1)
            else:
                cutoff_freq = freq_array[cutoff_index]
        else:
            cutoff_freq = freq_array[cutoff_index]
        
        return float(cutoff_freq)
        
    except Exception as e:
        logging.error(f"Error calculating cutoff frequency: {e}")
        return 0.0


def calculate_passband_gain(vout_array: np.ndarray) -> float:
    """
    计算通带增益。

    Args:
        vout_array: 复数电压输出NumPy数组。

    Returns:
        通带增益值 (dB)。
    """
    try:
        # 计算幅度（dB）
        magnitude_db = 20 * np.log10(np.abs(vout_array))
        
        # 对于低通滤波器，取频率数组前10%部分的增益平均值作为通带增益
        passband_samples = len(magnitude_db) // 10
        passband_gain = np.mean(magnitude_db[:passband_samples])
        
        return float(passband_gain)
        
    except Exception as e:
        logging.error(f"Error calculating passband gain: {e}")
        return 0.0


def calculate_phase_margin(freq_array: np.ndarray, vout_array: np.ndarray) -> float:
    """
    计算相位裕度。

    Args:
        freq_array: 频率点的NumPy数组。
        vout_array: 对应频率的复数电压输出NumPy数组。

    Returns:
        相位裕度值 (度)。
    """
    try:
        # 计算相位（度）
        phase_deg = np.angle(vout_array, deg=True)
        
        # 计算幅度
        magnitude = np.abs(vout_array)
        
        # 找到增益为1（0dB）的频率点
        gain_db = 20 * np.log10(magnitude)
        unity_gain_index = np.argmin(np.abs(gain_db))
        
        # 在该频率点的相位
        phase_at_unity = phase_deg[unity_gain_index]
        
        # 相位裕度 = 180° + 相位（因为相位通常是负值）
        phase_margin = 180.0 + phase_at_unity
        
        return float(phase_margin)
        
    except Exception as e:
        logging.error(f"Error calculating phase margin: {e}")
        return 0.0


def _extract_performance_metrics(simulation_results: SimulationResult, design_goal: DesignGoal) -> Dict[str, float]:
    """
    从仿真结果中提取关键性能指标
    """
    metrics = {}
    
    if not simulation_results.data:
        return metrics
    
    try:
        data = simulation_results.data
        
        if 'frequency' in data and 'vout' in data:
            freq_array = np.array(data['frequency'])
            vout_array = np.array(data['vout'])
            
            # 根据设计目标提取相应的指标
            if design_goal.target_metric == 'cutoff_frequency':
                cutoff_freq = calculate_cutoff_frequency(freq_array, vout_array)
                metrics['cutoff_frequency'] = cutoff_freq
                
            elif design_goal.target_metric == 'gain':
                passband_gain = calculate_passband_gain(vout_array)
                metrics['passband_gain'] = passband_gain
                
            elif design_goal.target_metric == 'phase_margin':
                phase_margin = calculate_phase_margin(freq_array, vout_array)
                metrics['phase_margin'] = phase_margin
            
            # 总是计算一些基本指标
            if 'cutoff_frequency' not in metrics:
                metrics['cutoff_frequency'] = calculate_cutoff_frequency(freq_array, vout_array)
            if 'passband_gain' not in metrics:
                metrics['passband_gain'] = calculate_passband_gain(vout_array)
                
    except Exception as e:
        logging.error(f"Error extracting metrics: {e}")
        metrics['error'] = str(e)
    
    return metrics


def _evaluate_design_goal(design_goal: DesignGoal, metrics: Dict[str, float]) -> tuple[bool, str]:
    """
    评估设计目标是否达成并生成反馈
    """
    target_metric = design_goal.target_metric
    target_value = design_goal.target_value
    tolerance = design_goal.tolerance or 0.05
    
    if target_metric not in metrics:
        return False, f"Could not measure {target_metric} from simulation results."
    
    actual_value = metrics[target_metric]
    
    # 计算误差
    error_ratio = abs(actual_value - target_value) / target_value
    
    if error_ratio <= tolerance:
        # 目标达成
        return True, f"Design goal achieved! {target_metric} = {actual_value:.2f} {design_goal.target_unit} (target: {target_value} ± {tolerance*100:.1f}%)"
    else:
        # 目标未达成，生成改进建议
        if actual_value > target_value:
            direction = "too high"
            suggestion = _get_adjustment_suggestion(design_goal, "decrease")
        else:
            direction = "too low"
            suggestion = _get_adjustment_suggestion(design_goal, "increase")
        
        feedback = f"{target_metric} is {direction}: {actual_value:.2f} {design_goal.target_unit} (target: {target_value}). {suggestion}"
        return False, feedback


def _get_adjustment_suggestion(design_goal: DesignGoal, direction: str) -> str:
    """
    根据设计目标和调整方向生成具体的调整建议
    """
    circuit_type = design_goal.circuit_type
    target_metric = design_goal.target_metric
    
    if target_metric == 'cutoff_frequency':
        if circuit_type == 'low_pass_rc':
            if direction == "increase":
                return "Try decreasing R or C values to increase cutoff frequency."
            else:
                return "Try increasing R or C values to decrease cutoff frequency."
        elif circuit_type == 'high_pass_rc':
            if direction == "increase":
                return "Try decreasing R or C values to increase cutoff frequency."
            else:
                return "Try increasing R or C values to decrease cutoff frequency."
    
    return "Adjust component values accordingly."
