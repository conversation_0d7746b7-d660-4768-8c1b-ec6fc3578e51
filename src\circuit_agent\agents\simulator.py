"""
SimulationAgent实现

实现SimulationAgent的核心逻辑，这是一个确定性节点，直接调用NgspiceSimulator工具执行电路仿真。
"""

import logging
from typing import Dict, Any

from ..schemas.models import AppState
from ..tools.ngspice_simulator import NgspiceSimulator

# 在应用启动时实例化一次工具
simulator_tool = NgspiceSimulator()


def simulation_node(state: AppState) -> Dict[str, Any]:
    """
    SimulationAgent在LangGraph中的实现节点。
    这是一个确定性节点，它不调用LLM，而是直接调用NgspiceSimulator工具
    来执行电路仿真。

    Args:
        state: 当前的LangGraph应用状态，使用 state['circuit_model'] 和
               state['simulation_params']。

    Returns:
        一个字典，用于更新状态中的 'simulation_results'。
    """
    logger = logging.getLogger(__name__)
    logger.info("--- Executing Simulation Node ---")
    
    circuit_model = state.get('circuit_model')
    simulation_params = state.get('simulation_params')
    
    if not circuit_model:
        logger.error("No circuit model found in state")
        return {
            'simulation_results': {
                'analysis_type': 'unknown',
                'data': None,
                'vectors': None,
                'error': 'No circuit model provided'
            }
        }
    
    if not simulation_params:
        logger.error("No simulation parameters found in state")
        return {
            'simulation_results': {
                'analysis_type': 'unknown',
                'data': None,
                'vectors': None,
                'error': 'No simulation parameters provided'
            }
        }
    
    try:
        logger.info(f"Running {simulation_params.analysis_type} analysis")
        logger.info(f"Circuit: {circuit_model.title}")
        logger.info(f"Components: {len(circuit_model.components)}")
        
        # 根据分析类型调用相应的仿真方法
        if simulation_params.analysis_type == 'ac':
            simulation_result = simulator_tool.run_ac_analysis(circuit_model, simulation_params)
        elif simulation_params.analysis_type == 'tran':
            simulation_result = simulator_tool.run_transient_analysis(circuit_model, simulation_params)
        elif simulation_params.analysis_type == 'op':
            simulation_result = simulator_tool.run_dc_operating_point(circuit_model, simulation_params)
        else:
            logger.error(f"Unsupported analysis type: {simulation_params.analysis_type}")
            simulation_result = {
                'analysis_type': simulation_params.analysis_type,
                'data': None,
                'vectors': None,
                'error': f'Unsupported analysis type: {simulation_params.analysis_type}'
            }
        
        if simulation_result.error:
            logger.error(f"Simulation failed: {simulation_result.error}")
        else:
            logger.info("Simulation completed successfully")
            if simulation_result.data:
                logger.info(f"Result vectors: {simulation_result.vectors}")
        
        return {
            'simulation_results': simulation_result
        }
        
    except Exception as e:
        logger.error(f"Error in simulation_node: {e}")
        return {
            'simulation_results': {
                'analysis_type': simulation_params.analysis_type if simulation_params else 'unknown',
                'data': None,
                'vectors': None,
                'error': f'Simulation node error: {str(e)}'
            }
        }


def validate_circuit_model(circuit_model) -> bool:
    """
    验证电路模型的有效性
    
    Args:
        circuit_model: 要验证的电路模型
        
    Returns:
        bool: 如果电路模型有效返回True，否则返回False
    """
    if not circuit_model:
        return False
    
    if not hasattr(circuit_model, 'components') or not circuit_model.components:
        return False
    
    # 检查是否有基本的电路连接
    nodes = set()
    for comp in circuit_model.components:
        if not hasattr(comp, 'nodes') or len(comp.nodes) != 2:
            return False
        nodes.update(comp.nodes)
    
    # 至少应该有输入和输出节点
    required_nodes = {'in', 'out', 'gnd'}
    if not required_nodes.issubset(nodes) and len(nodes) < 2:
        return False
    
    return True


def validate_simulation_params(simulation_params) -> bool:
    """
    验证仿真参数的有效性
    
    Args:
        simulation_params: 要验证的仿真参数
        
    Returns:
        bool: 如果仿真参数有效返回True，否则返回False
    """
    if not simulation_params:
        return False
    
    if not hasattr(simulation_params, 'analysis_type'):
        return False
    
    valid_analysis_types = ['ac', 'tran', 'op']
    if simulation_params.analysis_type not in valid_analysis_types:
        return False
    
    # 对于AC分析，检查频率参数
    if simulation_params.analysis_type == 'ac':
        if not hasattr(simulation_params, 'start_frequency') or not hasattr(simulation_params, 'stop_frequency'):
            return False
        
        if simulation_params.start_frequency <= 0 or simulation_params.stop_frequency <= 0:
            return False
        
        if simulation_params.start_frequency >= simulation_params.stop_frequency:
            return False
    
    return True


def get_simulation_summary(simulation_result) -> str:
    """
    获取仿真结果的摘要信息
    
    Args:
        simulation_result: 仿真结果对象
        
    Returns:
        str: 仿真结果摘要
    """
    if not simulation_result:
        return "No simulation result available"
    
    if simulation_result.error:
        return f"Simulation failed: {simulation_result.error}"
    
    if not simulation_result.data:
        return "Simulation completed but no data returned"
    
    summary_parts = [
        f"Analysis type: {simulation_result.analysis_type}",
        f"Vectors: {', '.join(simulation_result.vectors) if simulation_result.vectors else 'None'}"
    ]
    
    # 添加数据点数量信息
    if isinstance(simulation_result.data, dict):
        for key, value in simulation_result.data.items():
            if hasattr(value, '__len__'):
                summary_parts.append(f"{key}: {len(value)} points")
    
    return "; ".join(summary_parts)
